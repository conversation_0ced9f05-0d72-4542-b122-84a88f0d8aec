// M3 Max Specific Build Optimizations for macOS
// Hardware: 16 cores (12 performance + 4 efficiency), 128GB RAM
// NATIVE APPLE SILICON PERFORMANCE

// Xcode Build System Optimizations
CLANG_USE_RESPONSE_FILE = YES
CLANG_INDEX_STORE_ENABLE = NO

// Parallel Build Settings - Maximize M3 Max cores for native builds
SWIFT_COMPILATION_MODE = singlefile
CLANG_ENABLE_MODULES = YES
CLANG_MODULES_AUTOLINK = YES

// Memory Optimizations for 128GB RAM
SWIFT_OPTIMIZATION_LEVEL = -Onone
SWIFT_DISABLE_SAFETY_CHECKS = YES

// Linker Optimizations for macOS
OTHER_LDFLAGS = $(inherited) -Wl,-no_deduplicate
LD_RUNPATH_SEARCH_PATHS = $(inherited) @executable_path/../Frameworks

// Asset Catalog Optimizations
ASSETCATALOG_COMPILER_OPTIMIZATION = time
ASSETCATALOG_COMPILER_SKIP_APP_STORE_DEPLOYMENT = YES
COMBINE_HIDPI_IMAGES = YES

// Code Signing Optimizations (for development)
CODE_SIGN_STYLE = Automatic
DEVELOPMENT_TEAM = $(DEVELOPMENT_TEAM)

// Build Performance
COMPILER_INDEX_STORE_ENABLE = NO
SWIFT_INSTALL_OBJC_HEADER = NO

// Apple Silicon Specific - NATIVE M3 MAX
ARCHS = arm64
VALID_ARCHS = arm64
EXCLUDED_ARCHS = x86_64

// macOS Deployment
MACOSX_DEPLOYMENT_TARGET = 10.14

// Debug Information
DEBUG_INFORMATION_FORMAT = dwarf
DWARF_DSYM_FOLDER_PATH = $(CONFIGURATION_BUILD_DIR)

// Module Optimizations
DEFINES_MODULE = YES
CLANG_ENABLE_MODULE_DEBUGGING = NO

// Swift Compiler Optimizations
SWIFT_ACTIVE_COMPILATION_CONDITIONS = $(inherited) M3_MAX_OPTIMIZED MACOS_NATIVE
SWIFT_WHOLE_MODULE_OPTIMIZATION = NO

// Build System
CLANG_MODULES_BUILD_SESSION_FILE = /tmp/xcode_modules_build_session_macos

// macOS Specific Performance
COPY_PHASE_STRIP = NO
STRIP_INSTALLED_PRODUCT = NO

// Sandbox and Entitlements
ENABLE_HARDENED_RUNTIME = YES
ENABLE_APP_SANDBOX = YES
