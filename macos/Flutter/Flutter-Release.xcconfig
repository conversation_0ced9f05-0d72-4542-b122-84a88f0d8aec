#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"
#include "ephemeral/Flutter-Generated.xcconfig"
#include "../M3MaxOptimizations.xcconfig"

// M3 Max Optimizations for macOS Release Builds
// Optimized for 16 cores and 128GB RAM - NATIVE PERFORMANCE

// Whole module optimization for maximum performance
SWIFT_COMPILATION_MODE = wholemodule
SWIFT_OPTIMIZATION_LEVEL = -O

// Build performance optimizations
COMPILER_INDEX_STORE_ENABLE = NO
SWIFT_DISABLE_SAFETY_CHECKS = YES

// Memory and CPU optimizations for M3 Max native builds
SWIFT_WHOLE_MODULE_OPTIMIZATION = YES

// Parallel build settings - leverage all 16 cores
CLANG_ENABLE_MODULE_DEBUGGING = NO
CLANG_MODULES_BUILD_SESSION_FILE = /tmp/xcode_modules_build_session_macos

// Asset compilation optimizations
ASSETCATALOG_COMPILER_OPTIMIZATION = space

// Linker optimizations for release builds
LD_RUNPATH_SEARCH_PATHS = $(inherited) @executable_path/../Frameworks
ENABLE_TESTABILITY = NO

// Release-specific optimizations
GCC_OPTIMIZATION_LEVEL = s
SWIFT_ACTIVE_COMPILATION_CONDITIONS = RELEASE

// macOS-specific optimizations
MACOSX_DEPLOYMENT_TARGET = 10.14
COMBINE_HIDPI_IMAGES = YES

// Native Apple Silicon optimizations
ARCHS = arm64
VALID_ARCHS = arm64

// Code size and performance optimizations
DEAD_CODE_STRIPPING = YES
STRIP_INSTALLED_PRODUCT = YES
COPY_PHASE_STRIP = YES
