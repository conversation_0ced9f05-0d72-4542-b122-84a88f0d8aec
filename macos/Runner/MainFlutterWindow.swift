import Cocoa
import FlutterMacOS

class MainFlutterWindow: NSWindow {
  override func awakeFromNib() {
    let flutterViewController = FlutterViewController()
    
    // Set mobile aspect ratio (iPhone 14 Pro dimensions)
    let mobileWidth: CGFloat = 393
    let mobileHeight: CGFloat = 852
    
    // Center the window on screen
    if let screen = NSScreen.main {
      let screenFrame = screen.visibleFrame
      let x = (screenFrame.width - mobileWidth) / 2 + screenFrame.minX
      let y = (screenFrame.height - mobileHeight) / 2 + screenFrame.minY
      let windowFrame = NSRect(x: x, y: y, width: mobileWidth, height: mobileHeight)
      
      self.contentViewController = flutterViewController
      self.setFrame(windowFrame, display: true)
      
      // Set minimum and maximum size to maintain mobile aspect ratio
      self.minSize = NSSize(width: mobileWidth * 0.7, height: mobileHeight * 0.7)
      self.maxSize = NSSize(width: mobileWidth * 1.5, height: mobileHeight * 1.5)
      
      // Maintain aspect ratio when resizing
      self.aspectRatio = NSSize(width: mobileWidth, height: mobileHeight)
    }

    RegisterGeneratedPlugins(registry: flutterViewController)

    super.awakeFromNib()
  }
}
