# 🃏 Flash Cards App

A modern Flutter flashcard application built with clean MVVM architecture, supporting iOS, Android, and macOS platforms.

## ✨ Features

- 📚 Create and manage flashcard decks
- 🎯 Multiple study modes (Review, Learn, Test, Weak Cards)
- 📊 Progress tracking and analytics
- 🌙 Dark/Light theme support
- 🔄 Real-time synchronization
- 📱 Cross-platform support (iOS, Android, macOS)

## 🏗️ Architecture

This project follows **MVVM (Model-View-ViewModel)** architecture with:

- **Clean Architecture** principles
- **Dependency Injection** with GetIt
- **Repository Pattern** for data access
- **Use Cases** for business logic
- **Provider** for state management

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (3.24.3+)
- Dart SDK
- iOS development: Xcode
- Android development: Android Studio
- macOS development: Xcode

### Setup & Run

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd flash-flutter
   chmod +x scripts/*.sh
   ./scripts/dev_setup.sh
   ```

2. **Start development**
   ```bash
   ./scripts/realtime_dev.sh
   ```

3. **Or run manually**
   ```bash
   flutter pub get
   flutter run
   ```

## 📁 Project Structure

```
lib/
├── core/                    # Core utilities and configurations
│   ├── di/                 # Dependency injection
│   ├── theme/              # App theming
│   ├── services/           # Core services
│   └── viewmodels/         # Base ViewModels
├── data/                   # Data layer
│   ├── datasources/        # Local/Remote data sources
│   ├── models/             # Data models
│   └── repositories/       # Repository implementations
├── domain/                 # Business logic layer
│   ├── entities/           # Business entities
│   ├── repositories/       # Repository interfaces
│   └── usecases/          # Business use cases
├── presentation/           # Presentation layer
│   ├── viewmodels/        # ViewModels
│   ├── views/             # UI screens
│   └── widgets/           # Reusable widgets
└── main.dart              # App entry point
```

## 🛠️ Development Tools

- **`scripts/dev_setup.sh`** - Initial development environment setup
- **`scripts/realtime_dev.sh`** - Real-time development with hot reload
- **`scripts/build_all.sh`** - Multi-platform build automation
- **`scripts/dev_start.sh`** - Quick development startup

## 📖 Documentation

- [Development Guide](DEVELOPMENT_GUIDE.md) - Comprehensive development workflow
- [Architecture Overview](#architecture) - MVVM implementation details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `flutter test`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
