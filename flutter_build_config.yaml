# Flutter Build Configuration for M3 Max Optimization
# This file contains build optimizations for maximum performance

build_settings:
  # Core build optimizations
  parallel_builds: true
  max_workers: 16  # Utilize all M3 Max cores
  cache_enabled: true
  incremental_builds: true
  
  # Memory optimizations
  heap_size: "32G"
  metaspace_size: "8G"
  code_cache_size: "2G"
  
  # Compiler optimizations
  dart_optimizations:
    - "--no-sound-null-safety"  # For faster compilation during development
    - "--enable-asserts"        # Debug builds only
    - "--dart-define=flutter.inspector.structuredErrors=false"
  
  # Platform-specific optimizations
  android:
    gradle_parallel: true
    gradle_workers: 16
    r8_full_mode: true
    build_cache: true
    dex_transform: true
    
  ios:
    parallel_code_signing: false  # Avoid conflicts
    bitcode_enabled: false        # Faster builds
    
  macos:
    native_compilation: true
    
  web:
    renderer: "html"              # Faster than canvaskit for development
    source_maps: false            # Faster builds
    
# Development workflow optimizations
development:
  hot_reload: true
  hot_restart: true
  auto_rebuild: true
  test_watch: true
  
  # File watching optimizations
  watch_patterns:
    - "lib/**/*.dart"
    - "test/**/*.dart"
    - "assets/**/*"
    - "pubspec.yaml"
  
  ignore_patterns:
    - "build/**"
    - ".dart_tool/**"
    - "**/.DS_Store"
    - "**/Thumbs.db"

# Cache configurations
cache:
  flutter_cache: "$HOME/.flutter_build_cache"
  pub_cache: "$HOME/.pub-cache"
  gradle_cache: "$HOME/.gradle/caches"
  
  # Cache cleanup intervals (in days)
  cleanup_interval: 7
  max_cache_size: "50G"

# Performance monitoring
monitoring:
  build_time_tracking: true
  memory_usage_tracking: true
  cpu_usage_tracking: true
  
  # Performance thresholds (in seconds)
  thresholds:
    debug_build: 60
    release_build: 300
    hot_reload: 2
    
# Build artifacts
artifacts:
  output_directory: "builds"
  keep_debug_symbols: false     # Smaller builds
  compress_assets: true
  
  # Artifact retention (in days)
  retention_days: 30
