default_platform(:ios)

platform :ios do
  desc "Build and upload to TestFlight"
  lane :beta do
    setup_ci if ENV['CI']
    
    # Ensure we have the latest Flutter dependencies
    sh("cd ../../ && flutter pub get")
    
    # Clean previous builds
    sh("cd ../../ && flutter clean")
    
    # Build the iOS app
    sh("cd ../../ && flutter build ios --release")
    
    # Build and upload to TestFlight
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Release",
      clean: true,
      export_method: "app-store",
      skip_profile_detection: true,
      export_options: {
        provisioningProfiles: {}
      }
    )
    
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      skip_submission: true
    )
  end
  
  desc "Build for development"
  lane :dev do
    sh("cd ../../ && flutter pub get")
    sh("cd ../../ && flutter build ios --debug")
    
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Debug",
      clean: true,
      export_method: "development"
    )
  end
  
  desc "Setup certificates and profiles"
  lane :certificates do
    setup_ci if ENV['CI']
    
    match(
      type: "appstore",
      readonly: is_ci
    )
    
    match(
      type: "development",
      readonly: is_ci
    )
  end
  
  desc "Run tests"
  lane :test do
    run_tests(
      workspace: "Runner.xcworkspace",
      devices: ["iPhone 15"],
      scheme: "Runner"
    )
  end
end 