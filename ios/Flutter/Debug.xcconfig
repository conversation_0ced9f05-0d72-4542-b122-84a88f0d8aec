#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"
#include "Generated.xcconfig"
#include "../M3MaxOptimizations.xcconfig"

// M3 Max Optimizations for iOS Debug Builds
// Optimized for 16 cores and 128GB RAM

// Parallel compilation settings
SWIFT_COMPILATION_MODE = singlefile
SWIFT_OPTIMIZATION_LEVEL = -Onone

// Build performance optimizations
COMPILER_INDEX_STORE_ENABLE = NO
ENABLE_BITCODE = NO
SWIFT_DISABLE_SAFETY_CHECKS = YES

// Memory and CPU optimizations for M3 Max
SWIFT_WHOLE_MODULE_OPTIMIZATION = NO

// Parallel build settings
CLANG_ENABLE_MODULE_DEBUGGING = NO
CLANG_MODULES_BUILD_SESSION_FILE = /tmp/xcode_modules_build_session

// Asset compilation optimizations
ASSETCATALOG_COMPILER_OPTIMIZATION = time

// Linker optimizations for faster debug builds
LD_RUNPATH_SEARCH_PATHS = $(inherited) @executable_path/Frameworks
ENABLE_TESTABILITY = YES

// Debug-specific optimizations
GCC_OPTIMIZATION_LEVEL = 0
SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG
