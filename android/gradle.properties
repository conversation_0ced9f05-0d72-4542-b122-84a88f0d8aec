# Optimized for M3 Max with 16 cores and 128GB RAM
# JVM optimizations for maximum performance
org.gradle.jvmargs=-Xmx32G -XX:MaxMetaspaceSize=8G -XX:ReservedCodeCacheSize=2G -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat

# Gradle build optimizations
org.gradle.parallel=true
org.gradle.workers.max=16
org.gradle.configureondemand=true
org.gradle.caching=true
org.gradle.daemon=true

# Android build optimizations
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
android.enableBuildCache=true
android.experimental.enableParallelResourceProcessing=true

# Kotlin compiler optimizations
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.parallel.tasks.in.project=true

# Dex optimizations
android.enableDexingArtifactTransform=true
android.enableDexingArtifactTransform.desugaring=true

# Build performance
android.enableBuildScriptClasspathCheck=false
android.experimental.enableSourceSetPathsMap=true
