default_platform(:android)

platform :android do
  desc "Build and upload to Play Store Internal Testing"
  lane :beta do
    # Ensure we have the latest Flutter dependencies
    sh("cd ../../ && flutter pub get")
    
    # Clean previous builds
    sh("cd ../../ && flutter clean")
    
    # Build the Android app bundle
    sh("cd ../../ && flutter build appbundle --release")
    
    # Upload to Google Play Console
    upload_to_play_store(
      track: 'internal',
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end
  
  desc "Build APK for development"
  lane :dev do
    sh("cd ../../ && flutter pub get")
    sh("cd ../../ && flutter build apk --debug")
  end
  
  desc "Build APK for release"
  lane :release do
    sh("cd ../../ && flutter pub get")
    sh("cd ../../ && flutter clean")
    sh("cd ../../ && flutter build apk --release --split-per-abi")
  end
  
  desc "Run tests"
  lane :test do
    sh("cd ../../ && flutter test")
  end
  
  desc "Deploy to Play Store Production"
  lane :production do
    # Ensure we have the latest Flutter dependencies
    sh("cd ../../ && flutter pub get")
    
    # Clean previous builds
    sh("cd ../../ && flutter clean")
    
    # Build the Android app bundle
    sh("cd ../../ && flutter build appbundle --release")
    
    # Upload to Google Play Console
    upload_to_play_store(
      track: 'production',
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      skip_upload_metadata: false,
      skip_upload_changelogs: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end
end 