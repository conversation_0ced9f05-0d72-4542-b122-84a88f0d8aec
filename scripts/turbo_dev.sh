#!/bin/bash

# 🔥 TURBO DEV SCRIPT - Ultra-fast development workflow
# Optimized for M3 Max with hot reload, incremental builds, and instant feedback

echo "🔥 TURBO DEV MODE - Ultra-fast Development"
echo "Hardware: M3 Max (16 cores, 128GB RAM)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_turbo() {
    echo -e "${PURPLE}[TURBO]${NC} $1"
}

print_dev() {
    echo -e "${CYAN}[DEV]${NC} $1"
}

# Set environment variables for maximum development performance
export FLUTTER_BUILD_PARALLEL=true
export FLUTTER_HOT_RELOAD_ENABLED=true
export FLUTTER_HOT_RESTART_ENABLED=true
export GRADLE_OPTS="-Xmx16g -XX:MaxMetaspaceSize=4g -XX:+UseG1GC"

# Development cache optimization
export FLUTTER_BUILD_CACHE_DIR="$HOME/.flutter_build_cache"
export PUB_CACHE="$HOME/.pub-cache"

print_turbo "Development environment optimized for instant feedback"

# Development mode selection
echo ""
echo "🎯 Select development mode:"
echo "1) Hot reload (instant UI changes, <1s)"
echo "2) Debug build + run (fast startup, ~10-30s)"
echo "3) Watch mode (auto-rebuild on changes)"
echo "4) Test-driven development (TDD mode)"
echo "5) Performance profiling mode"

read -p "Enter your choice (1-5): " dev_mode

# Platform selection for development
echo ""
echo "🎯 Select development platform:"
echo "1) Android (fastest emulator/device)"
echo "2) iOS Simulator (native performance)"
echo "3) macOS Desktop (instant startup)"
echo "4) Chrome Web (fastest iteration)"

read -p "Enter your choice (1-4): " platform_choice

case $platform_choice in
    1) PLATFORM="android" ;;
    2) PLATFORM="ios" ;;
    3) PLATFORM="macos" ;;
    4) PLATFORM="chrome" ;;
    *) 
        print_status "Invalid choice. Using Chrome for fastest iteration."
        PLATFORM="chrome"
        ;;
esac

# Pre-development setup
print_turbo "Setting up turbo development environment..."

# Ensure dependencies are ready
if [ ! -d ".dart_tool" ] || [ ! -f "pubspec.lock" ]; then
    print_status "Installing dependencies..."
    flutter pub get
fi

# Enable platform if needed
case $PLATFORM in
    "macos")
        flutter config --enable-macos-desktop
        ;;
    "chrome")
        flutter config --enable-web
        ;;
esac

# Check for connected devices/emulators
print_status "Checking available devices..."
flutter devices

case $dev_mode in
    1)
        print_turbo "🔥 Starting HOT RELOAD mode..."
        print_dev "Changes will be reflected instantly (<1s)"
        
        case $PLATFORM in
            "android")
                flutter run --hot --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "ios")
                flutter run --hot --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "macos")
                flutter run -d macos --hot --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "chrome")
                flutter run -d chrome --hot --dart-define=flutter.inspector.structuredErrors=false --web-renderer html
                ;;
        esac
        ;;
    2)
        print_turbo "🚀 Starting DEBUG BUILD + RUN mode..."
        
        case $PLATFORM in
            "android")
                flutter run --debug --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "ios")
                flutter run --debug --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "macos")
                flutter run -d macos --debug --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "chrome")
                flutter run -d chrome --debug --dart-define=flutter.inspector.structuredErrors=false --web-renderer html
                ;;
        esac
        ;;
    3)
        print_turbo "👀 Starting WATCH mode..."
        print_dev "Auto-rebuilding on file changes..."
        
        # Create a watch script that monitors file changes
        cat > /tmp/flutter_watch.sh << 'EOF'
#!/bin/bash
PLATFORM=$1

build_and_run() {
    echo "🔄 Rebuilding due to file changes..."
    case $PLATFORM in
        "android")
            flutter run --debug --dart-define=flutter.inspector.structuredErrors=false &
            ;;
        "ios")
            flutter run --debug --dart-define=flutter.inspector.structuredErrors=false &
            ;;
        "macos")
            flutter run -d macos --debug --dart-define=flutter.inspector.structuredErrors=false &
            ;;
        "chrome")
            flutter run -d chrome --debug --dart-define=flutter.inspector.structuredErrors=false --web-renderer html &
            ;;
    esac
    APP_PID=$!
}

# Initial build
build_and_run

# Watch for changes
fswatch -o lib/ | while read f; do
    if [ ! -z "$APP_PID" ]; then
        kill $APP_PID 2>/dev/null
    fi
    sleep 1
    build_and_run
done
EOF
        
        chmod +x /tmp/flutter_watch.sh
        /tmp/flutter_watch.sh $PLATFORM
        ;;
    4)
        print_turbo "🧪 Starting TEST-DRIVEN DEVELOPMENT mode..."
        print_dev "Running tests with watch mode..."
        
        # Start test watcher
        flutter test --reporter=expanded &
        TEST_PID=$!
        
        # Watch for test file changes
        fswatch -o test/ lib/ | while read f; do
            echo "🔄 Re-running tests due to changes..."
            kill $TEST_PID 2>/dev/null
            sleep 1
            flutter test --reporter=expanded &
            TEST_PID=$!
        done
        ;;
    5)
        print_turbo "📊 Starting PERFORMANCE PROFILING mode..."
        print_dev "Building with performance monitoring..."
        
        case $PLATFORM in
            "android")
                flutter run --profile --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "ios")
                flutter run --profile --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "macos")
                flutter run -d macos --profile --dart-define=flutter.inspector.structuredErrors=false
                ;;
            "chrome")
                flutter run -d chrome --profile --dart-define=flutter.inspector.structuredErrors=false --web-renderer html
                ;;
        esac
        ;;
esac

print_turbo "🏁 TURBO DEV SESSION COMPLETED!"
print_success "Development session ended"

# Cleanup
if [ -f "/tmp/flutter_watch.sh" ]; then
    rm /tmp/flutter_watch.sh
fi

echo ""
print_turbo "📊 Development Tips:"
echo "  🔥 Use 'r' for hot reload during development"
echo "  🚀 Use 'R' for hot restart when needed"
echo "  🧪 Use 'p' to toggle performance overlay"
echo "  📱 Use 'o' to toggle platform (iOS/Android)"
echo "  🎯 Use 'q' to quit gracefully"

echo ""
print_dev "🎉 Happy coding with TURBO speed!"
