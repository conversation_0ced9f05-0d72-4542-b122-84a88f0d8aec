#!/bin/bash

echo "🚀 Starting Real-time Flutter Development Environment"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check dependencies
check_dependencies() {
    print_info "Checking dependencies..."
    
    if ! command -v flutter &> /dev/null; then
        echo "❌ Flutter is not installed. Please install Flutter first."
        exit 1
    fi
    
    if ! command -v fswatch &> /dev/null; then
        print_warning "fswatch not found. Installing..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install fswatch
        else
            echo "Please install fswatch manually for your system"
            exit 1
        fi
    fi
    
    print_success "All dependencies ready"
}

# Setup development environment
setup_dev_environment() {
    print_info "Setting up development environment..."
    
    # Enable hot reload optimizations
    flutter config --enable-hot-reload-on-save
    flutter config --enable-macos-desktop
    
    # Get dependencies
    flutter pub get
    
    print_success "Development environment ready"
}

# Start file watcher in background
start_file_watcher() {
    print_info "Starting file watcher..."
    
    fswatch -o \
        --event Created \
        --event Updated \
        --event Removed \
        --exclude ".*\.git/.*" \
        --exclude ".*\.dart_tool/.*" \
        --exclude ".*/build/.*" \
        --exclude ".*\.vscode/.*" \
        --exclude ".*\.DS_Store" \
        lib/ assets/ pubspec.yaml | while read num ; do
        
        echo "📝 Files changed at $(date '+%H:%M:%S'), triggering hot reload..."
        
        # Send 'r' to all flutter processes for hot reload
        pkill -f "flutter run" -USR1 2>/dev/null || true
        
        sleep 1  # Debounce rapid file changes
    done &
    
    WATCHER_PID=$!
    print_success "File watcher started (PID: $WATCHER_PID)"
}

# Start Flutter development
start_flutter_dev() {
    local device_choice=$1
    
    print_info "Starting Flutter development..."
    
    case $device_choice in
        "ios")
            print_info "Starting iOS Simulator..."
            open -a Simulator
            sleep 3
            flutter run --device-id=ios --hot --enable-software-rendering
            ;;
        "android")
            print_info "Starting Android development..."
            flutter run --device-id=android --hot --enable-software-rendering
            ;;
        "macos")
            print_info "Starting macOS development..."
            flutter run --device-id=macos --hot --enable-software-rendering
            ;;
        "auto")
            print_info "Auto-detecting device..."
            flutter run --hot --enable-software-rendering
            ;;
        *)
            print_info "Starting on all available devices..."
            flutter run --hot --enable-software-rendering
            ;;
    esac
}

# Cleanup function
cleanup() {
    print_info "Cleaning up..."
    if [ ! -z "$WATCHER_PID" ]; then
        kill $WATCHER_PID 2>/dev/null
        print_success "File watcher stopped"
    fi
    exit 0
}

# Trap cleanup on script exit
trap cleanup SIGINT SIGTERM

# Main menu
echo "🔥 Real-time Flutter Development"
echo "Choose your development platform:"
echo "1) iOS Simulator"
echo "2) Android Emulator"
echo "3) macOS Desktop"
echo "4) Auto-detect device"
echo "5) All available devices"

read -p "Enter your choice (1-5): " choice

case $choice in
    1) PLATFORM="ios" ;;
    2) PLATFORM="android" ;;
    3) PLATFORM="macos" ;;
    4) PLATFORM="auto" ;;
    5) PLATFORM="all" ;;
    *) 
        print_warning "Invalid choice. Using auto-detect."
        PLATFORM="auto"
        ;;
esac

# Run setup
check_dependencies
setup_dev_environment
start_file_watcher

print_success "🎉 Real-time development environment is ready!"
print_info "Features enabled:"
echo "  ✅ Automatic hot reload on file changes"
echo "  ✅ Optimized build configurations"
echo "  ✅ Multi-platform support"
echo "  ✅ File watching for lib/, assets/, pubspec.yaml"
echo ""
print_info "Press Ctrl+C to stop the development environment"
echo ""

# Start Flutter development
start_flutter_dev $PLATFORM

# Keep script running
wait 