#!/bin/bash

echo "🔥 Starting optimized Flutter development..."

# Function to detect available devices
detect_device() {
    echo "📱 Detecting devices..."
    flutter devices --machine | jq -r '.[].id' 2>/dev/null || flutter devices
}

# Function to start development on specific platform
start_dev() {
    local platform=$1
    local device_id=$2
    
    echo "🚀 Starting development on $platform..."
    
    # Enable hot reload and hot restart
    export FLUTTER_HOT_RELOAD_ON_SAVE=true
    
    # Start with optimized flags
    flutter run \
        --target=lib/main.dart \
        --device-id="$device_id" \
        --hot \
        --enable-software-rendering \
        --dart-define=flutter.inspector.structuredErrors=true \
        --verbose
}

# Menu for platform selection
echo "Choose your development platform:"
echo "1) iOS Simulator"
echo "2) Android Emulator"
echo "3) macOS Desktop"
echo "4) Auto-detect device"
echo "5) All platforms (parallel)"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "Starting iOS Simulator..."
        open -a Simulator
        sleep 3
        start_dev "iOS" "ios"
        ;;
    2)
        echo "Starting Android Emulator..."
        # Try to start default emulator
        emulator -list-avds | head -1 | xargs -I {} emulator -avd {} &
        sleep 5
        start_dev "Android" "android"
        ;;
    3)
        echo "Starting macOS Desktop..."
        start_dev "macOS" "macos"
        ;;
    4)
        echo "Auto-detecting device..."
        device_id=$(flutter devices --machine | jq -r '.[0].id' 2>/dev/null || echo "")
        if [ -z "$device_id" ]; then
            echo "No devices found. Please connect a device or start a simulator."
            exit 1
        fi
        start_dev "Auto-detected" "$device_id"
        ;;
    5)
        echo "Starting parallel development on all platforms..."
        # Start iOS
        open -a Simulator &
        # Start Android
        emulator -list-avds | head -1 | xargs -I {} emulator -avd {} &
        sleep 5
        
        # Run on multiple devices in parallel
        flutter run --device-id=ios &
        flutter run --device-id=android &
        flutter run --device-id=macos &
        
        wait
        ;;
    *)
        echo "Invalid choice. Exiting."
        exit 1
        ;;
esac 