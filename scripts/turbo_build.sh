#!/bin/bash

# 🚀 TURBO BUILD SCRIPT - Optimized for M3 Max (16 cores, 128GB RAM)
# This script maximizes build performance using all available system resources

echo "🚀 TURBO BUILD - Maximum Performance Mode"
echo "Hardware: M3 Max (16 cores, 128GB RAM)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_turbo() {
    echo -e "${PURPLE}[TURBO]${NC} $1"
}

# Performance monitoring
monitor_performance() {
    local pid=$1
    local name=$2
    
    while kill -0 $pid 2>/dev/null; do
        cpu_usage=$(ps -p $pid -o %cpu= 2>/dev/null | tr -d ' ')
        mem_usage=$(ps -p $pid -o %mem= 2>/dev/null | tr -d ' ')
        if [ ! -z "$cpu_usage" ]; then
            echo -e "${CYAN}[MONITOR]${NC} $name - CPU: ${cpu_usage}% MEM: ${mem_usage}%"
        fi
        sleep 5
    done
}

# Set environment variables for maximum performance
export FLUTTER_BUILD_PARALLEL=true
export GRADLE_OPTS="-Xmx32g -XX:MaxMetaspaceSize=8g -XX:+UseG1GC -XX:+UseStringDeduplication"
export JAVA_OPTS="-Xmx32g -XX:MaxMetaspaceSize=8g"

# iOS/macOS specific optimizations for M3 Max
export XCODE_XCCONFIG_FILE_IOS="ios/M3MaxOptimizations.xcconfig"
export XCODE_XCCONFIG_FILE_MACOS="macos/M3MaxOptimizations.xcconfig"
export XCODEBUILD_OPTS="-parallelizeTargets -jobs 16"
export SWIFT_COMPILATION_MODE="singlefile"
export ARCHS="arm64"

# Flutter specific optimizations
export FLUTTER_BUILD_CACHE_DIR="$HOME/.flutter_build_cache"
export PUB_CACHE="$HOME/.pub-cache"

# Create cache directories if they don't exist
mkdir -p "$FLUTTER_BUILD_CACHE_DIR"
mkdir -p "$PUB_CACHE"

print_turbo "Environment optimized for maximum performance"

# Build type selection with performance hints
echo ""
echo "🎯 Select build type:"
echo "1) Debug builds (fastest compilation, ~30-60s)"
echo "2) Release builds (optimized, ~2-5min)"
echo "3) Profile builds (performance testing, ~1-3min)"
echo "4) Hot reload development (instant, <1s)"

read -p "Enter your choice (1-4): " build_type

case $build_type in
    1) BUILD_MODE="debug" ;;
    2) BUILD_MODE="release" ;;
    3) BUILD_MODE="profile" ;;
    4) 
        print_turbo "Starting hot reload development mode..."
        exec ./scripts/turbo_dev.sh
        exit 0
        ;;
    *) 
        print_error "Invalid choice. Using debug mode for speed."
        BUILD_MODE="debug"
        ;;
esac

print_turbo "Building in $BUILD_MODE mode with maximum parallelization..."

# Platform selection with parallel options
echo ""
echo "🎯 Select platforms to build:"
echo "1) Android only (fastest single platform)"
echo "2) iOS only (macOS required)"
echo "3) macOS only (fastest native)"
echo "4) All platforms (parallel execution)"
echo "5) Android + iOS (mobile focus)"

read -p "Enter your choice (1-5): " platform_choice

# Pre-build optimizations
print_turbo "Performing pre-build optimizations..."

# Clean and prepare with parallel operations
print_status "Cleaning and preparing build environment..."
flutter clean &
clean_pid=$!

# Parallel pub get and pod operations
flutter pub get &
pub_pid=$!

if [[ "$OSTYPE" == "darwin"* ]] && ([ "$platform_choice" = "2" ] || [ "$platform_choice" = "4" ] || [ "$platform_choice" = "5" ]); then
    cd ios
    pod install --repo-update &
    pod_pid=$!
    cd ..
fi

# Wait for cleaning to complete
wait $clean_pid
print_success "Clean completed"

# Wait for pub get
wait $pub_pid
print_success "Dependencies resolved"

# Wait for pod install if running
if [ ! -z "$pod_pid" ]; then
    wait $pod_pid
    print_success "iOS dependencies resolved"
fi

# Code generation (if needed)
if [ -f "build.yaml" ] || grep -q "build_runner" pubspec.yaml; then
    print_status "Running code generation..."
    dart run build_runner build --delete-conflicting-outputs &
    codegen_pid=$!
    wait $codegen_pid
    print_success "Code generation completed"
fi

# Create optimized build directory
mkdir -p builds/turbo-$BUILD_MODE
build_start_time=$(date +%s)

# Enhanced build function with performance monitoring
turbo_build_platform() {
    local platform=$1
    local start_time=$(date +%s)
    
    print_turbo "🚀 Building $platform with maximum performance..."
    
    case $platform in
        "android")
            # Android build with maximum parallelization
            if [ "$BUILD_MODE" = "release" ]; then
                # Use all cores for release builds
                flutter build apk --$BUILD_MODE --split-per-abi --dart-define=flutter.inspector.structuredErrors=false &
                apk_pid=$!
                
                flutter build appbundle --$BUILD_MODE --dart-define=flutter.inspector.structuredErrors=false &
                bundle_pid=$!
                
                # Monitor performance
                monitor_performance $apk_pid "APK Build" &
                monitor_performance $bundle_pid "Bundle Build" &
                
                wait $apk_pid
                apk_result=$?
                wait $bundle_pid
                bundle_result=$?
                
                if [ $apk_result -eq 0 ] && [ $bundle_result -eq 0 ]; then
                    print_success "Android builds completed successfully"
                    cp -r build/app/outputs builds/turbo-$BUILD_MODE/android/
                    return 0
                else
                    print_error "Android build failed"
                    return 1
                fi
            else
                flutter build apk --$BUILD_MODE --dart-define=flutter.inspector.structuredErrors=false
                if [ $? -eq 0 ]; then
                    print_success "Android $BUILD_MODE build completed"
                    mkdir -p builds/turbo-$BUILD_MODE/android
                    cp -r build/app/outputs builds/turbo-$BUILD_MODE/android/
                    return 0
                else
                    print_error "Android build failed"
                    return 1
                fi
            fi
            ;;
        "ios")
            if [[ "$OSTYPE" == "darwin"* ]]; then
                print_turbo "🍎 Building iOS with M3 Max optimizations..."

                # Prepare iOS dependencies
                cd ios
                pod install --repo-update &
                pod_pid=$!
                cd ..
                wait $pod_pid

                # Build with M3 Max optimizations
                flutter build ios --$BUILD_MODE --no-codesign \
                    --dart-define=flutter.inspector.structuredErrors=false \
                    --dart-define=M3_MAX_OPTIMIZED=true

                if [ $? -eq 0 ]; then
                    print_success "iOS build completed with M3 Max optimizations"
                    cp -r build/ios builds/turbo-$BUILD_MODE/
                    return 0
                else
                    print_error "iOS build failed"
                    return 1
                fi
            else
                print_warning "iOS build skipped (not on macOS)"
                return 0
            fi
            ;;
        "macos")
            if [[ "$OSTYPE" == "darwin"* ]]; then
                print_turbo "🖥️  Building macOS with NATIVE M3 Max optimizations..."

                flutter config --enable-macos-desktop

                # Prepare macOS dependencies
                cd macos
                pod install --repo-update &
                pod_pid=$!
                cd ..
                wait $pod_pid

                # Build with M3 Max native optimizations
                flutter build macos --$BUILD_MODE \
                    --dart-define=flutter.inspector.structuredErrors=false \
                    --dart-define=M3_MAX_OPTIMIZED=true \
                    --dart-define=MACOS_NATIVE=true

                if [ $? -eq 0 ]; then
                    print_success "macOS build completed with NATIVE M3 Max optimizations"
                    cp -r build/macos builds/turbo-$BUILD_MODE/
                    return 0
                else
                    print_error "macOS build failed"
                    return 1
                fi
            else
                print_warning "macOS build skipped (not on macOS)"
                return 0
            fi
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    print_turbo "$platform build completed in ${duration}s"
}

# Execute builds based on selection
case $platform_choice in
    1)
        print_turbo "Building Android with maximum performance..."
        turbo_build_platform "android"
        ;;
    2)
        print_turbo "Building iOS with maximum performance..."
        turbo_build_platform "ios"
        ;;
    3)
        print_turbo "Building macOS with maximum performance..."
        turbo_build_platform "macos"
        ;;
    4)
        print_turbo "Building ALL platforms in parallel..."
        
        # Maximum parallelization - all platforms simultaneously
        turbo_build_platform "android" &
        android_pid=$!
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            turbo_build_platform "ios" &
            ios_pid=$!
            
            turbo_build_platform "macos" &
            macos_pid=$!
            
            # Monitor all builds
            monitor_performance $android_pid "Android" &
            monitor_performance $ios_pid "iOS" &
            monitor_performance $macos_pid "macOS" &
            
            # Wait for all builds
            wait $android_pid
            android_result=$?
            wait $ios_pid
            ios_result=$?
            wait $macos_pid
            macos_result=$?
            
            if [ $android_result -eq 0 ] && [ $ios_result -eq 0 ] && [ $macos_result -eq 0 ]; then
                print_success "🎉 ALL PLATFORMS built successfully!"
            else
                print_error "Some builds failed"
            fi
        else
            wait $android_pid
            if [ $? -eq 0 ]; then
                print_success "Android build completed successfully!"
            fi
        fi
        ;;
    5)
        print_turbo "Building mobile platforms (Android + iOS) in parallel..."
        
        turbo_build_platform "android" &
        android_pid=$!
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            turbo_build_platform "ios" &
            ios_pid=$!
            
            wait $android_pid
            android_result=$?
            wait $ios_pid
            ios_result=$?
            
            if [ $android_result -eq 0 ] && [ $ios_result -eq 0 ]; then
                print_success "🎉 Mobile platforms built successfully!"
            else
                print_error "Some mobile builds failed"
            fi
        else
            wait $android_pid
            if [ $? -eq 0 ]; then
                print_success "Android build completed successfully!"
            fi
        fi
        ;;
esac

build_end_time=$(date +%s)
total_duration=$((build_end_time - build_start_time))

print_turbo "🏁 TURBO BUILD COMPLETED!"
print_success "Total build time: ${total_duration}s"
print_status "Build artifacts saved in 'builds/turbo-$BUILD_MODE/' directory"

# Performance summary
echo ""
print_turbo "📊 Performance Summary:"
echo "  🔥 Hardware utilized: M3 Max (16 cores, 128GB RAM)"
echo "  ⚡ Parallel execution: Enabled"
echo "  🚀 Build optimizations: Maximum"
echo "  💾 Caching: Enabled"

# Display build sizes
if [ -d "builds/turbo-$BUILD_MODE" ]; then
    echo ""
    print_status "📦 Build sizes:"
    du -sh builds/turbo-$BUILD_MODE/* 2>/dev/null || echo "No build artifacts found"
fi

echo ""
print_turbo "🎉 TURBO BUILD PROCESS COMPLETED!"
print_status "Next steps:"
echo "  🧪 Test builds on respective platforms"
echo "  🚀 Deploy using: ./scripts/turbo_deploy.sh"
echo "  🔄 For development: ./scripts/turbo_dev.sh"
