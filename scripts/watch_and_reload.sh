#!/bin/bash

echo "👀 Starting file watcher for automatic reload..."

# Check if fswatch is available
if ! command -v fswatch &> /dev/null; then
    echo "Installing fswatch for file watching..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install fswatch
    else
        echo "Please install fswatch manually for your system"
        exit 1
    fi
fi

# Get the Flutter process ID if running
get_flutter_pid() {
    ps aux | grep "flutter run" | grep -v grep | awk '{print $2}' | head -1
}

# Send hot reload command to Flutter
send_hot_reload() {
    local flutter_pid=$(get_flutter_pid)
    if [ ! -z "$flutter_pid" ]; then
        echo "🔥 Triggering hot reload..."
        echo "r" > /proc/$flutter_pid/fd/0 2>/dev/null || {
            # Alternative method for macOS
            osascript -e "tell application \"Terminal\" to do script \"r\" in window 1" 2>/dev/null
        }
    else
        echo "⚠️ Flutter not running. Please start with 'flutter run' first."
    fi
}

# Watch for file changes
echo "Watching for changes in lib/, assets/, and pubspec.yaml..."
echo "Press Ctrl+C to stop watching."

fswatch -o \
    --event Created \
    --event Updated \
    --event Removed \
    --exclude ".*\.git/.*" \
    --exclude ".*\.dart_tool/.*" \
    --exclude ".*/build/.*" \
    --exclude ".*\.vscode/.*" \
    lib/ assets/ pubspec.yaml | while read num ; do
    
    echo "📝 Files changed, triggering hot reload..."
    send_hot_reload
    sleep 1  # Debounce rapid file changes
done 