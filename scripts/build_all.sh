#!/bin/bash

echo "🏗️ Building Flutter app for all platforms..."
echo "⚡ OPTIMIZED FOR M3 MAX (16 cores, 128GB RAM)"
echo "💡 For maximum performance, use: ./scripts/turbo_build.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Build type selection
echo "Select build type:"
echo "1) Debug builds (fast compilation)"
echo "2) Release builds (optimized)"
echo "3) Profile builds (performance testing)"

read -p "Enter your choice (1-3): " build_type

case $build_type in
    1) BUILD_MODE="debug" ;;
    2) BUILD_MODE="release" ;;
    3) BUILD_MODE="profile" ;;
    *) 
        print_error "Invalid choice. Using release mode."
        BUILD_MODE="release"
        ;;
esac

print_status "Building in $BUILD_MODE mode..."

# Set performance environment variables
export FLUTTER_BUILD_PARALLEL=true
export GRADLE_OPTS="-Xmx16g -XX:MaxMetaspaceSize=4g -XX:+UseG1GC"

# Clean previous builds with parallel operations
print_status "Cleaning previous builds with optimizations..."
flutter clean &
clean_pid=$!

flutter pub get &
pub_pid=$!

# Wait for operations to complete
wait $clean_pid
wait $pub_pid
print_status "✅ Build environment prepared"

# Create build directory
mkdir -p builds

# Function to build for specific platform
build_platform() {
    local platform=$1
    local start_time=$(date +%s)
    
    print_status "Building for $platform..."
    
    case $platform in
        "ios")
            if [[ "$OSTYPE" == "darwin"* ]]; then
                cd ios
                pod install --repo-update
                cd ..
                
                if [ "$BUILD_MODE" = "release" ]; then
                    flutter build ios --$BUILD_MODE --no-codesign
                else
                    flutter build ios --$BUILD_MODE
                fi
                
                if [ $? -eq 0 ]; then
                    print_success "iOS build completed"
                    # Copy build artifacts
                    cp -r build/ios builds/ios-$BUILD_MODE
                else
                    print_error "iOS build failed"
                    return 1
                fi
            else
                print_warning "iOS build skipped (not on macOS)"
            fi
            ;;
        "android")
            if [ "$BUILD_MODE" = "release" ]; then
                # Build both APK and App Bundle for release
                flutter build apk --$BUILD_MODE --split-per-abi
                flutter build appbundle --$BUILD_MODE
                
                if [ $? -eq 0 ]; then
                    print_success "Android build completed"
                    # Copy build artifacts
                    mkdir -p builds/android-$BUILD_MODE
                    cp -r build/app/outputs builds/android-$BUILD_MODE/
                else
                    print_error "Android build failed"
                    return 1
                fi
            else
                flutter build apk --$BUILD_MODE
                
                if [ $? -eq 0 ]; then
                    print_success "Android build completed"
                    mkdir -p builds/android-$BUILD_MODE
                    cp -r build/app/outputs builds/android-$BUILD_MODE/
                else
                    print_error "Android build failed"
                    return 1
                fi
            fi
            ;;
        "macos")
            if [[ "$OSTYPE" == "darwin"* ]]; then
                flutter config --enable-macos-desktop
                flutter build macos --$BUILD_MODE
                
                if [ $? -eq 0 ]; then
                    print_success "macOS build completed"
                    # Copy build artifacts
                    cp -r build/macos builds/macos-$BUILD_MODE
                else
                    print_error "macOS build failed"
                    return 1
                fi
            else
                print_warning "macOS build skipped (not on macOS)"
            fi
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    print_status "$platform build took ${duration}s"
}

# Parallel build option
echo ""
echo "Build execution:"
echo "1) Sequential builds (reliable)"
echo "2) Parallel builds (faster)"

read -p "Enter your choice (1-2): " execution_type

total_start_time=$(date +%s)

if [ "$execution_type" = "2" ]; then
    print_status "Starting parallel builds..."
    
    # Start builds in parallel
    build_platform "android" &
    android_pid=$!
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        build_platform "ios" &
        ios_pid=$!
        
        build_platform "macos" &
        macos_pid=$!
        
        # Wait for all builds to complete
        wait $android_pid
        android_result=$?
        
        wait $ios_pid
        ios_result=$?
        
        wait $macos_pid
        macos_result=$?
        
        # Check results
        if [ $android_result -eq 0 ] && [ $ios_result -eq 0 ] && [ $macos_result -eq 0 ]; then
            print_success "All builds completed successfully!"
        else
            print_error "Some builds failed"
        fi
    else
        wait $android_pid
        if [ $? -eq 0 ]; then
            print_success "Android build completed successfully!"
        else
            print_error "Android build failed"
        fi
    fi
else
    print_status "Starting sequential builds..."
    
    # Sequential builds
    if [[ "$OSTYPE" == "darwin"* ]]; then
        build_platform "macos"
        build_platform "ios"
    fi
    build_platform "android"
fi

total_end_time=$(date +%s)
total_duration=$((total_end_time - total_start_time))

print_success "Total build time: ${total_duration}s"
print_status "Build artifacts saved in 'builds/' directory"

# Display build sizes
if [ -d "builds" ]; then
    echo ""
    print_status "Build sizes:"
    du -sh builds/* 2>/dev/null || echo "No build artifacts found"
fi

echo ""
print_status "🎉 Build process completed!"
print_status "Next steps:"
echo "  - Test builds on respective platforms"
echo "  - Deploy using Fastlane: cd ios && bundle exec fastlane beta"
echo "  - Or deploy Android: cd android && bundle exec fastlane beta" 