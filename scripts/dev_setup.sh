#!/bin/bash

echo "🚀 Setting up optimized Flutter development environment..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    exit 1
fi

# Enable required platforms
echo "📱 Enabling platforms..."
flutter config --enable-macos-desktop
flutter config --enable-ios
flutter config --enable-android

# Clear Flutter cache for fresh start
echo "🧹 Clearing Flutter cache..."
flutter clean
rm -rf ~/.flutter
flutter doctor

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Generate code if needed
if grep -q "build_runner" pubspec.yaml; then
    echo "🔧 Running code generation..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
fi

# iOS setup
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Setting up iOS..."
    cd ios
    if command -v bundle &> /dev/null; then
        bundle install
    else
        gem install bundler
        bundle install
    fi
    pod install --repo-update
    cd ..
fi

# Android setup
echo "🤖 Setting up Android..."
cd android
if command -v bundle &> /dev/null; then
    bundle install
else
    gem install bundler
    bundle install
fi
cd ..

# Enable hot reload optimizations
echo "🔥 Enabling hot reload optimizations..."
flutter config --enable-hot-reload-on-save

# Create .vscode settings for optimal development
mkdir -p .vscode
cat > .vscode/settings.json << EOL
{
    "dart.flutterHotReloadOnSave": "always",
    "dart.hotReloadOnSave": "always",
    "flutter.automaticallySelectDevice": true,
    "dart.previewFlutterUiGuides": true,
    "dart.previewFlutterUiGuidesCustomTracking": true,
    "dart.debugExternalPackageLibraries": false,
    "dart.debugSdkLibraries": false,
    "dart.evaluateGettersInDebugViews": true,
    "dart.vmAdditionalArgs": [
        "--enable-asserts",
        "--lazy-async-stacks"
    ],
    "files.watcherExclude": {
        "**/build/**": true,
        "**/.dart_tool/**": true,
        "**/ios/Pods/**": true,
        "**/ios/.symlinks/**": true,
        "**/android/.gradle/**": true,
        "**/android/app/src/main/java/io/flutter/plugins/**": true
    }
}
EOL

# Create launch.json for debugging
cat > .vscode/launch.json << EOL
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug (iOS)",
            "request": "launch",
            "type": "dart",
            "deviceId": "ios",
            "args": ["--flavor", "development"]
        },
        {
            "name": "Debug (Android)",
            "request": "launch",
            "type": "dart",
            "deviceId": "android",
            "args": ["--flavor", "development"]
        },
        {
            "name": "Debug (macOS)",
            "request": "launch",
            "type": "dart",
            "deviceId": "macos",
            "args": ["--flavor", "development"]
        },
        {
            "name": "Profile Mode",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        }
    ]
}
EOL

echo "✅ Development environment setup complete!"
echo ""
echo "🔧 Quick commands:"
echo "  - Hot reload development: ./scripts/dev_start.sh"
echo "  - Build all platforms: ./scripts/build_all.sh"
echo "  - Run tests: flutter test"
echo "  - Analyze code: flutter analyze"
echo ""
echo "📱 To start developing:"
echo "  1. Connect your device or start simulator"
echo "  2. Run: flutter run"
echo "  3. Use 'r' for hot reload, 'R' for hot restart" 