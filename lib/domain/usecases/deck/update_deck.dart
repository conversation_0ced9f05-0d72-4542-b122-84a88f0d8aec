import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../entities/deck.dart';
import '../../repositories/deck_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class UpdateDeck implements UseCase<Deck, UpdateDeckParams> {
  final DeckRepository repository;

  UpdateDeck(this.repository);

  @override
  Future<Either<Failure, Deck>> call(UpdateDeckParams params) async {
    return await repository.updateDeck(params.deck);
  }
}

class UpdateDeckParams extends Equatable {
  final Deck deck;

  const UpdateDeckParams({required this.deck});

  @override
  List<Object> get props => [deck];
} 