import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../entities/deck.dart';
import '../../repositories/deck_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class CreateDeck implements UseCase<Deck, CreateDeckParams> {
  final DeckRepository repository;

  CreateDeck(this.repository);

  @override
  Future<Either<Failure, Deck>> call(CreateDeckParams params) async {
    return await repository.createDeck(params.name, params.description);
  }
}

class CreateDeckParams extends Equatable {
  final String name;
  final String description;

  const CreateDeckParams({
    required this.name,
    required this.description,
  });

  @override
  List<Object> get props => [name, description];
} 