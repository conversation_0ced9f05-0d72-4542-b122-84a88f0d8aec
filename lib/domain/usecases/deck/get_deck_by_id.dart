import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/deck.dart';
import '../../repositories/deck_repository.dart';

@injectable
class GetDeckById implements UseCase<Deck, GetDeckByIdParams> {
  final DeckRepository repository;

  GetDeckById(this.repository);

  @override
  Future<Either<Failure, Deck>> call(GetDeckByIdParams params) async {
    return await repository.getDeckById(params.deckId);
  }
}

class GetDeckByIdParams {
  final String deckId;

  GetDeckByIdParams({required this.deckId});
} 