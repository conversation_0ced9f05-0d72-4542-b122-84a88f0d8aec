import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/deck.dart';
import '../../repositories/deck_repository.dart';

@injectable
class GetDecks implements UseCase<List<Deck>, NoParams> {
  final DeckRepository repository;

  GetDecks(this.repository);

  @override
  Future<Either<Failure, List<Deck>>> call(NoParams params) async {
    return await repository.getDecks();
  }
} 