import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../repositories/deck_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class DeleteDeck implements VoidUseCase<DeleteDeckParams> {
  final DeckRepository repository;

  DeleteDeck(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteDeckParams params) async {
    return await repository.deleteDeck(params.deckId);
  }
}

class DeleteDeckParams extends Equatable {
  final String deckId;

  const DeleteDeckParams({required this.deckId});

  @override
  List<Object> get props => [deckId];
} 