import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/flashcard.dart';
import '../../repositories/flashcard_repository.dart';

@injectable
class CreateFlashcard implements UseCase<Flashcard, CreateFlashcardParams> {
  final FlashcardRepository repository;

  CreateFlashcard(this.repository);

  @override
  Future<Either<Failure, Flashcard>> call(CreateFlashcardParams params) async {
    return await repository.createFlashcard(
      params.deckId,
      params.front,
      params.back,
    );
  }
}

class CreateFlashcardParams {
  final String deckId;
  final String front;
  final String back;

  CreateFlashcardParams({
    required this.deckId,
    required this.front,
    required this.back,
  });
} 