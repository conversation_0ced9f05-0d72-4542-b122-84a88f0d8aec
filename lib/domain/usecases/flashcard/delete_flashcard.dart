import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../repositories/flashcard_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class DeleteFlashcard implements UseCase<void, DeleteFlashcardParams> {
  final FlashcardRepository repository;

  DeleteFlashcard(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteFlashcardParams params) async {
    return await repository.deleteFlashcard(params.flashcardId);
  }
}

class DeleteFlashcardParams {
  final String flashcardId;

  DeleteFlashcardParams({
    required this.flashcardId,
  });
} 