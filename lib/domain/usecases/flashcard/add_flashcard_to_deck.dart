import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../entities/flashcard.dart';
import '../../repositories/flashcard_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class AddFlashcardToDeck implements UseCase<Flashcard, AddFlashcardToDeckParams> {
  final FlashcardRepository repository;

  AddFlashcardToDeck(this.repository);

  @override
  Future<Either<Failure, Flashcard>> call(AddFlashcardToDeckParams params) async {
    return await repository.createFlashcard(
      params.deckId,
      params.front,
      params.back,
    );
  }
}

class AddFlashcardToDeckParams {
  final String deckId;
  final String front;
  final String back;

  AddFlashcardToDeckParams({
    required this.deckId,
    required this.front,
    required this.back,
  });
} 