import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../entities/flashcard.dart';
import '../../repositories/flashcard_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class UpdateFlashcard implements UseCase<Flashcard, UpdateFlashcardParams> {
  final FlashcardRepository repository;

  UpdateFlashcard(this.repository);

  @override
  Future<Either<Failure, Flashcard>> call(UpdateFlashcardParams params) async {
    return await repository.updateFlashcard(params.flashcard);
  }
}

class UpdateFlashcardParams {
  final Flashcard flashcard;

  UpdateFlashcardParams({
    required this.flashcard,
  });
} 