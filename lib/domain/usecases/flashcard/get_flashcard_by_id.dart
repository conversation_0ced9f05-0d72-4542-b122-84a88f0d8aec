import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../entities/flashcard.dart';
import '../../repositories/flashcard_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class GetFlashcardById implements UseCase<Flashcard, GetFlashcardByIdParams> {
  final FlashcardRepository repository;

  GetFlashcardById(this.repository);

  @override
  Future<Either<Failure, Flashcard>> call(GetFlashcardByIdParams params) async {
    return await repository.getFlashcardById(params.flashcardId);
  }
}

class GetFlashcardByIdParams {
  final String flashcardId;

  GetFlashcardByIdParams({
    required this.flashcardId,
  });
} 