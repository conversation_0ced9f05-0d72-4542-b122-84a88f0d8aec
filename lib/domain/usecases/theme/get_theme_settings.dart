import 'package:dartz/dartz.dart';
import '../../entities/theme_settings.dart';
import '../../repositories/theme_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class GetThemeSettings implements UseCase<ThemeSettings, NoParams> {
  final ThemeRepository repository;

  GetThemeSettings(this.repository);

  @override
  Future<Either<Failure, ThemeSettings>> call(NoParams params) async {
    return await repository.getThemeSettings();
  }
} 