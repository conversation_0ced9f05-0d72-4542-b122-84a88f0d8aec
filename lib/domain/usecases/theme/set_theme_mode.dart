import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../repositories/theme_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class SetThemeMode implements VoidUseCase<SetThemeModeParams> {
  final ThemeRepository repository;

  SetThemeMode(this.repository);

  @override
  Future<Either<Failure, void>> call(SetThemeModeParams params) async {
    return await repository.setThemeMode(params.themeMode);
  }
}

class SetThemeModeParams extends Equatable {
  final ThemeMode themeMode;

  const SetThemeModeParams({required this.themeMode});

  @override
  List<Object> get props => [themeMode];
} 