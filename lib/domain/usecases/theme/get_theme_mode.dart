import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';

import '../../repositories/theme_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class GetThemeMode implements UseCase<ThemeMode, NoParams> {
  final ThemeRepository repository;

  GetThemeMode(this.repository);

  @override
  Future<Either<Failure, ThemeMode>> call(NoParams params) async {
    return await repository.getThemeMode();
  }
} 