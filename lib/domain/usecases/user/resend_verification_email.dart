import '../../repositories/user_repository.dart';
import '../../../core/usecases/usecase.dart';
import '../../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';

class ResendVerificationEmail implements UseCase<void, ResendVerificationEmailParams> {
  final UserRepository repository;

  ResendVerificationEmail(this.repository);

  @override
  Future<Either<Failure, void>> call(ResendVerificationEmailParams params) async {
    return await repository.resendVerificationEmail(params.email);
  }
}

class ResendVerificationEmailParams {
  final String email;

  const ResendVerificationEmailParams({required this.email});
} 