import 'package:dartz/dartz.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class LogoutUser implements VoidUseCase<NoParams> {
  final UserRepository repository;

  LogoutUser(this.repository);

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    return await repository.logout();
  }
} 