import '../../repositories/user_repository.dart';
import '../../../core/usecases/usecase.dart';
import '../../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';

class VerifyEmail implements UseCase<void, VerifyEmailParams> {
  final UserRepository repository;

  VerifyEmail(this.repository);

  @override
  Future<Either<Failure, void>> call(VerifyEmailParams params) async {
    return await repository.verifyEmail(params.email, params.verificationCode);
  }
}

class VerifyEmailParams {
  final String email;
  final String verificationCode;

  const VerifyEmailParams({
    required this.email,
    required this.verificationCode,
  });
} 