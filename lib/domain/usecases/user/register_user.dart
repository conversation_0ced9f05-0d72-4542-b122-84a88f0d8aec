import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/user.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class RegisterUser implements UseCase<User, RegisterUserParams> {
  final UserRepository repository;

  RegisterUser(this.repository);

  @override
  Future<Either<Failure, User>> call(RegisterUserParams params) async {
    return await repository.register(params.name, params.email, params.password);
  }
}

class RegisterUserParams extends Equatable {
  final String name;
  final String email;
  final String password;

  const RegisterUserParams({
    required this.name,
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [name, email, password];
} 