import '../../repositories/user_repository.dart';
import '../../../core/usecases/usecase.dart';
import '../../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';

class ResetPassword implements UseCase<void, ResetPasswordParams> {
  final UserRepository repository;

  ResetPassword(this.repository);

  @override
  Future<Either<Failure, void>> call(ResetPasswordParams params) async {
    return await repository.resetPassword(
      params.email,
      params.verificationCode,
      params.newPassword,
    );
  }
}

class ResetPasswordParams {
  final String email;
  final String verificationCode;
  final String newPassword;

  const ResetPasswordParams({
    required this.email,
    required this.verificationCode,
    required this.newPassword,
  });
} 