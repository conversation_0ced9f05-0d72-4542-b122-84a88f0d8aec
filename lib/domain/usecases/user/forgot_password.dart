import '../../repositories/user_repository.dart';
import '../../../core/usecases/usecase.dart';
import '../../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';

class ForgotPassword implements UseCase<void, ForgotPasswordParams> {
  final UserRepository repository;

  ForgotPassword(this.repository);

  @override
  Future<Either<Failure, void>> call(ForgotPasswordParams params) async {
    return await repository.forgotPassword(params.email);
  }
}

class ForgotPasswordParams {
  final String email;

  const ForgotPasswordParams({required this.email});
} 