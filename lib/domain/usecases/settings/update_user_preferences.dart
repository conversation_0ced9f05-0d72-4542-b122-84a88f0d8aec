import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user_preferences.dart';
import '../../repositories/settings_repository.dart';

@injectable
class UpdateUserPreferences implements UseCase<UserPreferences, UpdateUserPreferencesParams> {
  final SettingsRepository repository;

  UpdateUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferences>> call(UpdateUserPreferencesParams params) async {
    return await repository.updateUserPreferences(params.preferences);
  }
}

class UpdateUserPreferencesParams {
  final UserPreferences preferences;

  UpdateUserPreferencesParams({required this.preferences});
}