import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user_preferences.dart';
import '../../repositories/settings_repository.dart';

@injectable
class GetUserPreferences implements UseCase<UserPreferences, NoParams> {
  final SettingsRepository repository;

  GetUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferences>> call(NoParams params) async {
    return await repository.getUserPreferences();
  }
} 