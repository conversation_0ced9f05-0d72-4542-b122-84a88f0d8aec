import 'package:dartz/dartz.dart';

import '../../entities/settings.dart';
import '../../repositories/settings_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

class UpdateSettings implements UseCase<Settings, UpdateSettingsParams> {
  final SettingsRepository repository;

  UpdateSettings(this.repository);

  @override
  Future<Either<Failure, Settings>> call(UpdateSettingsParams params) async {
    return await repository.updateSettings(params.settings);
  }
}

class UpdateSettingsParams {
  final Settings settings;

  UpdateSettingsParams({
    required this.settings,
  });
} 