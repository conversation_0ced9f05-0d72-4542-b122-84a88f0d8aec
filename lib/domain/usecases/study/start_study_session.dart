import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/flashcard.dart';
import '../../entities/study_session.dart';
import '../../repositories/study_repository.dart';

@injectable
class StartStudySession implements UseCase<StudySession, StartStudySessionParams> {
  final StudyRepository repository;

  StartStudySession(this.repository);

  @override
  Future<Either<Failure, StudySession>> call(StartStudySessionParams params) async {
    return await repository.startStudySession(params.deckId, params.cards);
  }
}

class StartStudySessionParams {
  final String deckId;
  final List<Flashcard>? cards;

  StartStudySessionParams({
    required this.deckId,
    this.cards,
  });
} 