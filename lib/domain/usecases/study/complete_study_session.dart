import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/study_repository.dart';

@injectable
class CompleteStudySession implements UseCase<void, CompleteStudySessionParams> {
  final StudyRepository repository;

  CompleteStudySession(this.repository);

  @override
  Future<Either<Failure, void>> call(CompleteStudySessionParams params) async {
    return await repository.completeStudySession(
      params.deckId,
      params.results,
      params.duration,
    );
  }
}

class CompleteStudySessionParams {
  final String deckId;
  final List<Map<String, dynamic>> results;
  final Duration duration;

  CompleteStudySessionParams({
    required this.deckId,
    required this.results,
    required this.duration,
  });
} 