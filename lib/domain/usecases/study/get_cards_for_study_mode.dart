import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/flashcard.dart';
import '../../entities/study_mode.dart';

class GetCardsForStudyMode implements UseCase<List<Flashcard>, GetCardsForStudyModeParams> {
  @override
  Future<Either<Failure, List<Flashcard>>> call(GetCardsForStudyModeParams params) async {
    try {
      final filteredCards = _filterCardsForMode(params.cards, params.mode);
      final sortedCards = _sortCardsByPriority(filteredCards, params.mode);
      final limitedCards = params.maxCards != null 
          ? sortedCards.take(params.maxCards!).toList()
          : sortedCards;
      
      return Right(limitedCards);
    } catch (e) {
      return Left(CacheFailure());
    }
  }

  List<Flashcard> _filterCardsForMode(List<Flashcard> cards, StudyMode mode) {
    switch (mode) {
      case StudyMode.review:
        // All cards that need review based on spaced repetition
        return cards.where((card) => card.needsReview).toList();
      
      case StudyMode.practice:
        // All cards for general practice
        return cards.toList();
      
      case StudyMode.test:
        // Random selection of all cards
        return cards.toList()..shuffle();
      
      case StudyMode.learn:
        // New cards or cards with low review count
        return cards.where((card) => card.reviewCount < 3).toList();
      
      case StudyMode.weakCards:
        // Cards marked as difficult or with low success rate
        return cards.where((card) => card.isDifficult).toList();
    }
  }

  List<Flashcard> _sortCardsByPriority(List<Flashcard> cards, StudyMode mode) {
    switch (mode) {
      case StudyMode.review:
        // Sort by last reviewed date (oldest first)
        cards.sort((a, b) {
          if (a.lastReviewed == null && b.lastReviewed == null) return 0;
          if (a.lastReviewed == null) return -1;
          if (b.lastReviewed == null) return 1;
          return a.lastReviewed!.compareTo(b.lastReviewed!);
        });
        break;
      
      case StudyMode.learn:
        // Sort by creation date (newest first for learning)
        cards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      
      case StudyMode.weakCards:
        // Sort by difficulty (hardest first)
        cards.sort((a, b) => b.difficulty.compareTo(a.difficulty));
        break;
      
      case StudyMode.practice:
      case StudyMode.test:
        // Shuffle for variety
        cards.shuffle();
        break;
    }
    
    return cards;
  }
}

class GetCardsForStudyModeParams {
  final List<Flashcard> cards;
  final StudyMode mode;
  final int? maxCards;

  const GetCardsForStudyModeParams({
    required this.cards,
    required this.mode,
    this.maxCards,
  });
} 