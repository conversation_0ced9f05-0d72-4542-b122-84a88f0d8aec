import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/flashcard.dart';
import '../../repositories/flashcard_repository.dart';

class RecordStudyResult implements UseCase<Flashcard, RecordStudyResultParams> {
  final FlashcardRepository repository;

  const RecordStudyResult(this.repository);

  @override
  Future<Either<Failure, Flashcard>> call(RecordStudyResultParams params) async {
    try {
      // Calculate new difficulty based on performance
      final newDifficulty = _calculateNewDifficulty(
        params.flashcard,
        params.isCorrect,
        params.responseTime,
      );

      // Update flashcard with new performance data
      final updatedCard = params.flashcard.copyWith(
        lastReviewed: DateTime.now(),
        reviewCount: params.flashcard.reviewCount + 1,
        difficulty: newDifficulty,
      );

      // Save to repository
      final result = await repository.updateFlashcard(updatedCard);
      
      return result.fold(
        (failure) => Left(failure),
        (card) => Right(card),
      );
    } catch (e) {
      return Left(CacheFailure());
    }
  }

  double _calculateNewDifficulty(Flashcard card, bool isCorrect, Duration responseTime) {
    double currentDifficulty = card.difficulty;
    
    // Base adjustment based on correctness
    double adjustment = isCorrect ? -0.1 : 0.2;
    
    // Adjust based on response time (faster = easier)
    if (responseTime.inSeconds < 3) {
      adjustment -= 0.05; // Very fast response, make easier
    } else if (responseTime.inSeconds > 10) {
      adjustment += 0.05; // Slow response, make harder
    }
    
    // Apply diminishing returns for cards reviewed many times
    if (card.reviewCount > 10) {
      adjustment *= 0.5;
    }
    
    // Calculate new difficulty
    double newDifficulty = currentDifficulty + adjustment;
    
    // Clamp between 0.0 and 1.0
    return newDifficulty.clamp(0.0, 1.0);
  }
}

class RecordStudyResultParams {
  final Flashcard flashcard;
  final bool isCorrect;
  final Duration responseTime;

  const RecordStudyResultParams({
    required this.flashcard,
    required this.isCorrect,
    required this.responseTime,
  });
} 