import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../entities/deck.dart';
import '../../repositories/deck_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';

@injectable
class UpdateDeckLastStudied implements UseCase<Deck, UpdateDeckLastStudiedParams> {
  final DeckRepository repository;

  UpdateDeckLastStudied(this.repository);

  @override
  Future<Either<Failure, Deck>> call(UpdateDeckLastStudiedParams params) async {
    final updatedDeck = params.deck.copyWith(
      lastStudied: DateTime.now(),
    );
    return await repository.updateDeck(updatedDeck);
  }
}

class UpdateDeckLastStudiedParams {
  final Deck deck;

  UpdateDeckLastStudiedParams({
    required this.deck,
  });
} 