import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/deck.dart';

class GetStudyStatistics implements UseCase<StudyStatistics, GetStudyStatisticsParams> {
  @override
  Future<Either<Failure, StudyStatistics>> call(GetStudyStatisticsParams params) async {
    try {
      final statistics = _calculateStatistics(params.deck);
      return Right(statistics);
    } catch (e) {
      return Left(CacheFailure());
    }
  }

  StudyStatistics _calculateStatistics(Deck deck) {
    final cards = deck.flashcards;
    
    if (cards.isEmpty) {
      return StudyStatistics.empty();
    }

    // Basic counts
    final totalCards = cards.length;
    final reviewedCards = cards.where((card) => card.hasBeenReviewed).length;
    final newCards = cards.where((card) => card.isNew).length;
    
    // Difficulty distribution
    final easyCards = cards.where((card) => card.isEasy).length;
    final mediumCards = cards.where((card) => card.isMedium).length;
    final hardCards = cards.where((card) => card.isDifficult).length;
    
    // Cards needing review
    final cardsNeedingReview = cards.where((card) => card.needsReview).length;
    
    // Average difficulty
    final averageDifficulty = cards.isEmpty 
        ? 0.0 
        : cards.map((card) => card.difficulty).reduce((a, b) => a + b) / cards.length;
    
    // Total review count
    final totalReviews = cards.map((card) => card.reviewCount).reduce((a, b) => a + b);
    
    // Study streak (days since last study)
    final daysSinceLastStudy = deck.lastStudied != null 
        ? DateTime.now().difference(deck.lastStudied!).inDays
        : null;
    
    // Mastery percentage (cards with difficulty < 0.3 and review count > 3)
    final masteredCards = cards.where((card) => 
        card.isEasy && card.reviewCount > 3).length;
    final masteryPercentage = totalCards > 0 ? (masteredCards / totalCards) * 100 : 0.0;

    return StudyStatistics(
      totalCards: totalCards,
      reviewedCards: reviewedCards,
      newCards: newCards,
      easyCards: easyCards,
      mediumCards: mediumCards,
      hardCards: hardCards,
      cardsNeedingReview: cardsNeedingReview,
      averageDifficulty: averageDifficulty,
      totalReviews: totalReviews,
      daysSinceLastStudy: daysSinceLastStudy,
      masteryPercentage: masteryPercentage,
      masteredCards: masteredCards,
    );
  }
}

class GetStudyStatisticsParams {
  final Deck deck;

  const GetStudyStatisticsParams({required this.deck});
}

class StudyStatistics {
  final int totalCards;
  final int reviewedCards;
  final int newCards;
  final int easyCards;
  final int mediumCards;
  final int hardCards;
  final int cardsNeedingReview;
  final double averageDifficulty;
  final int totalReviews;
  final int? daysSinceLastStudy;
  final double masteryPercentage;
  final int masteredCards;

  const StudyStatistics({
    required this.totalCards,
    required this.reviewedCards,
    required this.newCards,
    required this.easyCards,
    required this.mediumCards,
    required this.hardCards,
    required this.cardsNeedingReview,
    required this.averageDifficulty,
    required this.totalReviews,
    required this.daysSinceLastStudy,
    required this.masteryPercentage,
    required this.masteredCards,
  });

  factory StudyStatistics.empty() {
    return const StudyStatistics(
      totalCards: 0,
      reviewedCards: 0,
      newCards: 0,
      easyCards: 0,
      mediumCards: 0,
      hardCards: 0,
      cardsNeedingReview: 0,
      averageDifficulty: 0.0,
      totalReviews: 0,
      daysSinceLastStudy: null,
      masteryPercentage: 0.0,
      masteredCards: 0,
    );
  }

  // Getters for UI display
  double get progressPercentage => totalCards > 0 ? (reviewedCards / totalCards) * 100 : 0.0;
  
  String get difficultyLabel {
    if (averageDifficulty < 0.3) return 'Easy';
    if (averageDifficulty < 0.7) return 'Medium';
    return 'Hard';
  }
  
  bool get hasCardsToReview => cardsNeedingReview > 0;
  bool get hasNewCards => newCards > 0;
  bool get isWellStudied => masteryPercentage > 70.0;
} 