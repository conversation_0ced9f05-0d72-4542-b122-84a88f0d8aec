import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/study_repository.dart';

@injectable
class UpdateCardPerformance implements UseCase<void, UpdateCardPerformanceParams> {
  final StudyRepository repository;

  UpdateCardPerformance(this.repository);

  @override
  Future<Either<Failure, void>> call(UpdateCardPerformanceParams params) async {
    return await repository.updateCardPerformance(
      params.cardId,
      params.isCorrect,
    );
  }
}

class UpdateCardPerformanceParams {
  final String cardId;
  final bool isCorrect;

  UpdateCardPerformanceParams({
    required this.cardId,
    required this.isCorrect,
  });
} 