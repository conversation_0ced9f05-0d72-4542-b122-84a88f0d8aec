import 'package:equatable/equatable.dart';
import 'flashcard.dart';

class Deck extends Equatable {
  final String id;
  final String name;
  final String description;
  final List<Flashcard> flashcards;
  final DateTime createdAt;
  final DateTime? lastStudied;

  const Deck({
    required this.id,
    required this.name,
    required this.description,
    required this.flashcards,
    required this.createdAt,
    this.lastStudied,
  });

  Deck copyWith({
    String? id,
    String? name,
    String? description,
    List<Flashcard>? flashcards,
    DateTime? createdAt,
    DateTime? lastStudied,
  }) {
    return Deck(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      flashcards: flashcards ?? this.flashcards,
      createdAt: createdAt ?? this.createdAt,
      lastStudied: lastStudied ?? this.lastStudied,
    );
  }

  Deck addFlashcard(Flashcard flashcard) {
    return copyWith(
      flashcards: [...flashcards, flashcard],
    );
  }

  Deck updateFlashcard(Flashcard updatedCard) {
    final index = flashcards.indexWhere((card) => card.id == updatedCard.id);
    if (index == -1) return this;
    
    final newFlashcards = [...flashcards];
    newFlashcards[index] = updatedCard;
    
    return copyWith(flashcards: newFlashcards);
  }

  Deck removeFlashcard(String flashcardId) {
    return copyWith(
      flashcards: flashcards.where((card) => card.id != flashcardId).toList(),
    );
  }

  Deck markAsStudied() {
    return copyWith(lastStudied: DateTime.now());
  }

  // Business logic methods
  int get totalCards => flashcards.length;
  
  bool get isEmpty => flashcards.isEmpty;
  
  bool get isNotEmpty => flashcards.isNotEmpty;
  
  double get averageDifficulty {
    if (flashcards.isEmpty) return 0.0;
    return flashcards.map((card) => card.difficulty).reduce((a, b) => a + b) / flashcards.length;
  }
  
  List<Flashcard> get difficultCards {
    return flashcards.where((card) => card.difficulty > 0.7).toList();
  }
  
  List<Flashcard> get easyCards {
    return flashcards.where((card) => card.difficulty < 0.3).toList();
  }
  
  bool get hasBeenStudied => lastStudied != null;
  
  Duration? get timeSinceLastStudy {
    if (lastStudied == null) return null;
    return DateTime.now().difference(lastStudied!);
  }
  
  bool get wasStudiedToday {
    if (lastStudied == null) return false;
    final now = DateTime.now();
    final studiedDate = lastStudied!;
    return now.year == studiedDate.year &&
           now.month == studiedDate.month &&
           now.day == studiedDate.day;
  }
  
  int get cardsNeedingReview {
    return flashcards.where((card) => card.needsReview).length;
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    flashcards,
    createdAt,
    lastStudied,
  ];
} 