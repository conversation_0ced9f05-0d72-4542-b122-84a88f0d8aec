/// Study mode enumeration for different types of study sessions
enum StudyMode {
  /// Review mode - standard flashcard review
  review,
  
  /// Practice mode - focused practice session
  practice,
  
  /// Test mode - quiz-style testing
  test,
  
  /// Learn mode - learning new cards
  learn,
  
  /// Weak cards mode - focus on difficult cards
  weakCards,
}

/// Extension to provide display names for StudyMode
extension StudyModeExtension on StudyMode {
  String get displayName {
    switch (this) {
      case StudyMode.review:
        return 'Review';
      case StudyMode.practice:
        return 'Practice';
      case StudyMode.test:
        return 'Test';
      case StudyMode.learn:
        return 'Learn';
      case StudyMode.weakCards:
        return 'Weak Cards';
    }
  }
  
  String get description {
    switch (this) {
      case StudyMode.review:
        return 'Standard flashcard review session';
      case StudyMode.practice:
        return 'Focused practice with immediate feedback';
      case StudyMode.test:
        return 'Quiz-style testing without hints';
      case StudyMode.learn:
        return 'Learning new cards for the first time';
      case StudyMode.weakCards:
        return 'Focus on cards you find difficult';
    }
  }
} 