import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final DateTime joinDate;
  final int studyStreak;
  final int totalCardsStudied;
  final Map<String, dynamic> preferences;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    required this.joinDate,
    this.studyStreak = 0,
    this.totalCardsStudied = 0,
    this.preferences = const {},
  });

  User copyWith({
    String? name,
    String? email,
    String? avatarUrl,
    int? studyStreak,
    int? totalCardsStudied,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      joinDate: joinDate,
      studyStreak: studyStreak ?? this.studyStreak,
      totalCardsStudied: totalCardsStudied ?? this.totalCardsStudied,
      preferences: preferences ?? this.preferences,
    );
  }

  // Business logic methods
  bool get hasAvatar => avatarUrl != null && avatarUrl!.isNotEmpty;
  
  bool get isNewUser => DateTime.now().difference(joinDate).inDays < 7;
  
  bool get hasActiveStreak => studyStreak > 0;
  
  bool get isExperiencedUser => totalCardsStudied > 100;
  
  T? getPreference<T>(String key, {T? defaultValue}) {
    if (preferences.containsKey(key)) {
      return preferences[key] as T?;
    }
    return defaultValue;
  }
  
  bool getBoolPreference(String key, {bool defaultValue = false}) {
    return getPreference<bool>(key, defaultValue: defaultValue) ?? defaultValue;
  }
  
  User updatePreferences(Map<String, dynamic> newPreferences) {
    final updatedPreferences = Map<String, dynamic>.from(preferences);
    updatedPreferences.addAll(newPreferences);
    return copyWith(preferences: updatedPreferences);
  }
  
  User incrementStudyStats({
    int cardsStudied = 0,
    bool maintainStreak = false,
  }) {
    return copyWith(
      totalCardsStudied: totalCardsStudied + cardsStudied,
      studyStreak: maintainStreak ? studyStreak + 1 : studyStreak,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    avatarUrl,
    joinDate,
    studyStreak,
    totalCardsStudied,
    preferences,
  ];
} 