import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class Settings extends Equatable {
  final ThemeMode themeMode;
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String language;
  final int studyReminder; // Hours between reminders
  final bool autoAdvance;
  final int cardDisplayTime; // Seconds
  
  const Settings({
    this.themeMode = ThemeMode.system,
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.language = 'en',
    this.studyReminder = 24,
    this.autoAdvance = false,
    this.cardDisplayTime = 5,
  });

  Settings copyWith({
    ThemeMode? themeMode,
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? language,
    int? studyReminder,
    bool? autoAdvance,
    int? cardDisplayTime,
  }) {
    return Settings(
      themeMode: themeMode ?? this.themeMode,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      language: language ?? this.language,
      studyReminder: studyReminder ?? this.studyReminder,
      autoAdvance: autoAdvance ?? this.autoAdvance,
      cardDisplayTime: cardDisplayTime ?? this.cardDisplayTime,
    );
  }

  @override
  List<Object?> get props => [
        themeMode,
        notificationsEnabled,
        soundEnabled,
        vibrationEnabled,
        language,
        studyReminder,
        autoAdvance,
        cardDisplayTime,
      ];
} 