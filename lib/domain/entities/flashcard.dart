import 'package:equatable/equatable.dart';

class Flashcard extends Equatable {
  final String id;
  final String front;
  final String back;
  final DateTime createdAt;
  final DateTime? lastReviewed;
  final int reviewCount;
  final double difficulty; // 0.0 to 1.0, higher means more difficult

  const Flashcard({
    required this.id,
    required this.front,
    required this.back,
    required this.createdAt,
    this.lastReviewed,
    this.reviewCount = 0,
    this.difficulty = 0.5,
  });

  Flashcard copyWith({
    String? id,
    String? front,
    String? back,
    DateTime? createdAt,
    DateTime? lastReviewed,
    int? reviewCount,
    double? difficulty,
  }) {
    return Flashcard(
      id: id ?? this.id,
      front: front ?? this.front,
      back: back ?? this.back,
      createdAt: createdAt ?? this.createdAt,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      reviewCount: reviewCount ?? this.reviewCount,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  Flashcard markAsReviewed({double? newDifficulty}) {
    return copyWith(
      lastReviewed: DateTime.now(),
      reviewCount: reviewCount + 1,
      difficulty: newDifficulty ?? difficulty,
    );
  }

  Flashcard updateDifficulty(double newDifficulty) {
    return copyWith(difficulty: newDifficulty.clamp(0.0, 1.0));
  }

  // Business logic methods
  bool get hasBeenReviewed => lastReviewed != null;
  
  bool get isNew => reviewCount == 0;
  
  bool get isDifficult => difficulty > 0.7;
  
  bool get isEasy => difficulty < 0.3;
  
  bool get isMedium => difficulty >= 0.3 && difficulty <= 0.7;
  
  Duration? get timeSinceLastReview {
    if (lastReviewed == null) return null;
    return DateTime.now().difference(lastReviewed!);
  }
  
  bool get needsReview {
    if (lastReviewed == null) return true;
    
    final daysSinceReview = timeSinceLastReview!.inDays;
    
    // Review schedule based on difficulty
    if (isEasy) return daysSinceReview >= 7;
    if (isMedium) return daysSinceReview >= 3;
    if (isDifficult) return daysSinceReview >= 1;
    
    return daysSinceReview >= 3;
  }
  
  String get difficultyLabel {
    if (isEasy) return 'Easy';
    if (isMedium) return 'Medium';
    if (isDifficult) return 'Hard';
    return 'Medium';
  }

  @override
  List<Object?> get props => [
    id,
    front,
    back,
    createdAt,
    lastReviewed,
    reviewCount,
    difficulty,
  ];
} 