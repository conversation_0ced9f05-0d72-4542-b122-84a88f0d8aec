import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class ThemeSettings extends Equatable {
  final ThemeMode themeMode;

  const ThemeSettings({
    this.themeMode = ThemeMode.dark,
  });

  ThemeSettings copyWith({
    ThemeMode? themeMode,
  }) {
    return ThemeSettings(
      themeMode: themeMode ?? this.themeMode,
    );
  }

  // Business logic methods
  bool get isDarkMode {
    if (themeMode == ThemeMode.system) {
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == 
          Brightness.dark;
    }
    return themeMode == ThemeMode.dark;
  }
  
  bool get isLightMode {
    if (themeMode == ThemeMode.system) {
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == 
          Brightness.light;
    }
    return themeMode == ThemeMode.light;
  }
  
  bool get isSystemMode => themeMode == ThemeMode.system;

  ThemeSettings toggleTheme() {
    final newMode = isDarkMode ? ThemeMode.light : ThemeMode.dark;
    return copyWith(themeMode: newMode);
  }

  @override
  List<Object?> get props => [themeMode];
} 