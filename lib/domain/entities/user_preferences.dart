import 'package:equatable/equatable.dart';

class UserPreferences extends Equatable {
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String studyReminder;
  final int dailyGoal;
  final String theme; // 'light', 'dark', 'system'
  final String language;
  final bool autoPlayAudio;
  final bool showHints;
  final int sessionDuration; // in minutes

  const UserPreferences({
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.studyReminder = 'daily',
    this.dailyGoal = 10,
    this.theme = 'system',
    this.language = 'en',
    this.autoPlayAudio = false,
    this.showHints = true,
    this.sessionDuration = 30,
  });

  UserPreferences copyWith({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? studyReminder,
    int? dailyGoal,
    String? theme,
    String? language,
    bool? autoPlayAudio,
    bool? showHints,
    int? sessionDuration,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      studyReminder: studyReminder ?? this.studyReminder,
      dailyGoal: dailyGoal ?? this.dailyGoal,
      theme: theme ?? this.theme,
      language: language ?? this.language,
      autoPlayAudio: autoPlayAudio ?? this.autoPlayAudio,
      showHints: showHints ?? this.showHints,
      sessionDuration: sessionDuration ?? this.sessionDuration,
    );
  }

  // Business logic methods
  bool get hasNotificationsEnabled => notificationsEnabled;
  
  bool get hasCustomReminder => studyReminder == 'custom';
  
  bool get isHighGoal => dailyGoal >= 25;
  
  bool get isLowGoal => dailyGoal <= 5;
  
  String get studyReminderDisplayText {
    switch (studyReminder) {
      case 'never':
        return 'Never';
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'custom':
        return 'Custom';
      default:
        return 'Daily';
    }
  }
  
  String get dailyGoalDisplayText {
    return '$dailyGoal cards per day';
  }
  
  String get sessionDurationDisplayText {
    return '$sessionDuration minutes';
  }

  @override
  List<Object?> get props => [
    notificationsEnabled,
    soundEnabled,
    vibrationEnabled,
    studyReminder,
    dailyGoal,
    theme,
    language,
    autoPlayAudio,
    showHints,
    sessionDuration,
  ];
} 