import 'package:equatable/equatable.dart';
import 'flashcard.dart';

class StudySession extends Equatable {
  final String id;
  final String deckId;
  final List<Flashcard> cards;
  final DateTime startTime;
  final DateTime? endTime;
  final List<StudyResult> results;
  final bool isCompleted;

  const StudySession({
    required this.id,
    required this.deckId,
    required this.cards,
    required this.startTime,
    this.endTime,
    this.results = const [],
    this.isCompleted = false,
  });

  StudySession copyWith({
    String? id,
    String? deckId,
    List<Flashcard>? cards,
    DateTime? startTime,
    DateTime? endTime,
    List<StudyResult>? results,
    bool? isCompleted,
  }) {
    return StudySession(
      id: id ?? this.id,
      deckId: deckId ?? this.deckId,
      cards: cards ?? this.cards,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      results: results ?? this.results,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  StudySession addResult(StudyResult result) {
    return copyWith(
      results: [...results, result],
    );
  }

  StudySession complete() {
    return copyWith(
      endTime: DateTime.now(),
      isCompleted: true,
    );
  }

  // Business logic methods
  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }
  
  int get totalCards => cards.length;
  
  int get answeredCards => results.length;
  
  int get remainingCards => totalCards - answeredCards;
  
  int get correctAnswers => results.where((r) => r.isCorrect).length;
  
  int get incorrectAnswers => results.where((r) => !r.isCorrect).length;
  
  double get accuracy {
    if (results.isEmpty) return 0.0;
    return correctAnswers / results.length;
  }
  
  double get progress {
    if (totalCards == 0) return 0.0;
    return answeredCards / totalCards;
  }
  
  bool get isActive => !isCompleted && endTime == null;
  
  String get durationText {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
  
  String get accuracyText {
    return '${(accuracy * 100).toStringAsFixed(1)}%';
  }
  
  String get progressText {
    return '$answeredCards of $totalCards';
  }

  @override
  List<Object?> get props => [
    id,
    deckId,
    cards,
    startTime,
    endTime,
    results,
    isCompleted,
  ];
}

class StudyResult extends Equatable {
  final String cardId;
  final bool isCorrect;
  final DateTime timestamp;
  final Duration timeSpent;

  const StudyResult({
    required this.cardId,
    required this.isCorrect,
    required this.timestamp,
    required this.timeSpent,
  });

  StudyResult copyWith({
    String? cardId,
    bool? isCorrect,
    DateTime? timestamp,
    Duration? timeSpent,
  }) {
    return StudyResult(
      cardId: cardId ?? this.cardId,
      isCorrect: isCorrect ?? this.isCorrect,
      timestamp: timestamp ?? this.timestamp,
      timeSpent: timeSpent ?? this.timeSpent,
    );
  }

  @override
  List<Object?> get props => [
    cardId,
    isCorrect,
    timestamp,
    timeSpent,
  ];
} 