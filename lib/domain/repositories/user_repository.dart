import 'package:dartz/dartz.dart';
import '../entities/user.dart';
import '../../core/errors/failures.dart';

abstract class UserRepository {
  Future<Either<Failure, User?>> getCurrentUser();
  Future<Either<Failure, User>> login(String email, String password);
  Future<Either<Failure, User>> register(String name, String email, String password);
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, User>> updateProfile(User user);
  Future<Either<Failure, User>> updatePreferences(String userId, Map<String, dynamic> preferences);
  Future<Either<Failure, User>> incrementStudyStats(String userId, {int cardsStudied = 0, bool maintainStreak = false});
  Future<Either<Failure, void>> saveUser(User user);
  
  // New authentication methods
  Future<Either<Failure, void>> forgotPassword(String email);
  Future<Either<Failure, void>> resetPassword(String email, String verificationCode, String newPassword);
  Future<Either<Failure, void>> verifyEmail(String email, String verificationCode);
  Future<Either<Failure, void>> resendVerificationEmail(String email);
} 