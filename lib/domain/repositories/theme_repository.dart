import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../entities/theme_settings.dart';
import '../../core/errors/failures.dart';

abstract class ThemeRepository {
  Future<Either<Failure, ThemeSettings>> getThemeSettings();
  Future<Either<Failure, void>> saveThemeSettings(ThemeSettings settings);
  Future<Either<Failure, void>> setThemeMode(ThemeMode mode);
  Future<Either<Failure, ThemeMode>> getThemeMode();
} 