import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/flashcard.dart';
import '../entities/study_session.dart';

abstract class StudyRepository {
  Future<Either<Failure, StudySession>> startStudySession(
    String deckId,
    List<Flashcard>? cards,
  );
  
  Future<Either<Failure, void>> completeStudySession(
    String deckId,
    List<Map<String, dynamic>> results,
    Duration duration,
  );
  
  Future<Either<Failure, void>> updateCardPerformance(
    String cardId,
    bool isCorrect,
  );
  
  Future<Either<Failure, StudySession>> getStudySessionById(String id);
  
  Future<Either<Failure, List<StudySession>>> getStudySessionsByDeckId(String deckId);
  
  Future<Either<Failure, List<StudySession>>> getUserStudySessions();
  
  Future<Either<Failure, Map<String, dynamic>>> getStudyStatistics();
} 