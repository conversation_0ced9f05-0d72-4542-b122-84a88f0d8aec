import 'package:dartz/dartz.dart';

import '../entities/settings.dart';
import '../entities/user_preferences.dart';
import '../../core/errors/failures.dart';

abstract class SettingsRepository {
  Future<Either<Failure, Settings>> getSettings();
  Future<Either<Failure, Settings>> updateSettings(Settings settings);
  Future<Either<Failure, void>> resetSettings();
  Future<Either<Failure, UserPreferences>> getUserPreferences();
  Future<Either<Failure, UserPreferences>> updateUserPreferences(UserPreferences preferences);
} 