import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/flashcard.dart';

abstract class FlashcardRepository {
  Future<Either<Failure, Flashcard>> createFlashcard(
    String deckId,
    String front,
    String back,
  );
  
  Future<Either<Failure, Flashcard>> getFlashcardById(String id);
  
  Future<Either<Failure, List<Flashcard>>> getFlashcardsByDeckId(String deckId);
  
  Future<Either<Failure, Flashcard>> updateFlashcard(Flashcard flashcard);
  
  Future<Either<Failure, void>> deleteFlashcard(String id);
  
  Future<Either<Failure, List<Flashcard>>> searchFlashcards(String query);
} 