import 'package:dartz/dartz.dart';
import '../entities/deck.dart';
import '../entities/flashcard.dart';
import '../../core/errors/failures.dart';

abstract class DeckRepository {
  Future<Either<Failure, List<Deck>>> getDecks();
  Future<Either<Failure, Deck>> getDeckById(String id);
  Future<Either<Failure, Deck>> createDeck(String name, String description);
  Future<Either<Failure, Deck>> updateDeck(Deck deck);
  Future<Either<Failure, void>> deleteDeck(String id);
  Future<Either<Failure, Deck>> addFlashcardToDeck(String deckId, Flashcard flashcard);
  Future<Either<Failure, Deck>> updateFlashcardInDeck(String deckId, Flashcard flashcard);
  Future<Either<Failure, Deck>> removeFlashcardFromDeck(String deckId, String flashcardId);
  Future<Either<Failure, List<Deck>>> searchDecks(String query);
} 