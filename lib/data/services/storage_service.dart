import 'dart:convert';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/deck_model.dart';
import '../../domain/entities/deck.dart';

class StorageService {
  static const String _decksKey = 'decks';

  Future<List<Deck>> loadDecks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final decksJson = prefs.getString(_decksKey);
      
      if (decksJson == null) {
        developer.log('No decks found in storage', name: 'StorageService');
        return [];
      }

      final List<dynamic> decksList = json.decode(decksJson);
      final deckModels = decksList.map((json) => DeckModel.fromJson(json)).toList();
      
      // Convert data models to domain entities
      return deckModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      developer.log('Error loading decks: $e', name: 'StorageService', error: e);
      return [];
    }
  }

  Future<void> saveDecks(List<Deck> decks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Convert domain entities to data models
      final deckModels = decks.map((deck) => DeckModel.fromEntity(deck)).toList();
      final decksJson = json.encode(deckModels.map((model) => model.toJson()).toList());
      
      await prefs.setString(_decksKey, decksJson);
      developer.log('Decks saved successfully', name: 'StorageService');
    } catch (e) {
      developer.log('Error saving decks: $e', name: 'StorageService', error: e);
    }
  }

  Future<void> saveDeck(Deck deck) async {
    final decks = await loadDecks();
    final existingIndex = decks.indexWhere((d) => d.id == deck.id);
    
    if (existingIndex != -1) {
      decks[existingIndex] = deck;
    } else {
      decks.add(deck);
    }
    
    await saveDecks(decks);
  }

  Future<void> deleteDeck(String deckId) async {
    final decks = await loadDecks();
    decks.removeWhere((deck) => deck.id == deckId);
    await saveDecks(decks);
  }

  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_decksKey);
  }
} 