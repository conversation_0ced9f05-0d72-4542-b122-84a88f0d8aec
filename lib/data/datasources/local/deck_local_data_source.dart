import 'package:injectable/injectable.dart';
import 'package:hive/hive.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/errors/failures.dart';
import '../../models/deck_model.dart';

abstract class DeckLocalDataSource {
  Future<List<DeckModel>> getDecks();
  Future<DeckModel?> getDeckById(String id);
  Future<void> saveDeck(DeckModel deck);
  Future<void> deleteDeck(String id);
  Future<void> saveDecks(List<DeckModel> decks);
  Future<void> clearAllDecks();
}

@Injectable(as: DeckLocalDataSource)
class DeckLocalDataSourceImpl implements DeckLocalDataSource {
  late Box<Map> _deckBox;
  bool _isInitialized = false;

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      _deckBox = await Hive.openBox<Map>(AppConstants.deckBox);
      _isInitialized = true;
    }
  }

  @override
  Future<List<DeckModel>> getDecks() async {
    try {
      await _ensureInitialized();
      final decksData = _deckBox.values.toList();
      final decks = decksData
          .map((data) => DeckModel.fromJson(Map<String, dynamic>.from(data)))
          .toList();
      return decks;
    } catch (e) {

      throw const CacheFailure('Failed to load decks from local storage');
    }
  }

  @override
  Future<DeckModel?> getDeckById(String id) async {
    try {
      await _ensureInitialized();
      final deckData = _deckBox.get(id);
      if (deckData == null) return null;
      return DeckModel.fromJson(Map<String, dynamic>.from(deckData));
    } catch (e) {
      throw const CacheFailure('Failed to load deck from local storage');
    }
  }

  @override
  Future<void> saveDeck(DeckModel deck) async {
    try {
      await _ensureInitialized();
      await _deckBox.put(deck.id, deck.toJson());
    } catch (e) {
      throw const CacheFailure('Failed to save deck to local storage');
    }
  }

  @override
  Future<void> deleteDeck(String id) async {
    try {
      await _ensureInitialized();
      await _deckBox.delete(id);
    } catch (e) {
      throw const CacheFailure('Failed to delete deck from local storage');
    }
  }

  @override
  Future<void> saveDecks(List<DeckModel> decks) async {
    try {
      await _ensureInitialized();
      final deckMap = {for (var deck in decks) deck.id: deck.toJson()};
      await _deckBox.putAll(deckMap);
    } catch (e) {
      throw const CacheFailure('Failed to save decks to local storage');
    }
  }

  @override
  Future<void> clearAllDecks() async {
    try {
      await _ensureInitialized();
      await _deckBox.clear();
    } catch (e) {
      throw const CacheFailure('Failed to clear decks from local storage');
    }
  }
} 