import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/errors/failures.dart';
import '../../domain/entities/theme_settings.dart';
import '../../domain/repositories/theme_repository.dart';

class ThemeRepositoryImpl implements ThemeRepository {
  static const String _themeKey = 'theme_mode';

  @override
  Future<Either<Failure, ThemeSettings>> getThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      ThemeMode themeMode = ThemeMode.dark; // Default to dark mode
      if (savedTheme != null) {
        themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.dark,
        );
      }
      
      return Right(ThemeSettings(themeMode: themeMode));
    } catch (e) {
      return const Left(CacheFailure('Failed to load theme settings'));
    }
  }

  @override
  Future<Either<Failure, void>> saveThemeSettings(ThemeSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, settings.themeMode.toString());
      return const Right(null);
    } catch (e) {
      return const Left(CacheFailure('Failed to save theme settings'));
    }
  }

  @override
  Future<Either<Failure, void>> setThemeMode(ThemeMode mode) async {
    try {
      final settings = ThemeSettings(themeMode: mode);
      return await saveThemeSettings(settings);
    } catch (e) {
      return const Left(CacheFailure('Failed to set theme mode'));
    }
  }

  @override
  Future<Either<Failure, ThemeMode>> getThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      ThemeMode themeMode = ThemeMode.dark; // Default to dark mode
      if (savedTheme != null) {
        themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.dark,
        );
      }
      
      return Right(themeMode);
    } catch (e) {
      return const Left(CacheFailure('Failed to get theme mode'));
    }
  }
} 