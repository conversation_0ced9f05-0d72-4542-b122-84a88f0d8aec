import 'package:dartz/dartz.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../../core/errors/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';

class UserRepositoryImpl implements UserRepository {
  static const String _userKey = 'current_user';

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        final user = User(
          id: userMap['id'],
          name: userMap['name'],
          email: userMap['email'],
          avatarUrl: userMap['avatarUrl'],
          joinDate: DateTime.parse(userMap['joinDate']),
          studyStreak: userMap['studyStreak'] ?? 0,
          totalCardsStudied: userMap['totalCardsStudied'] ?? 0,
          preferences: Map<String, dynamic>.from(userMap['preferences'] ?? {}),
        );
        return Right(user);
      }
      
      return const Right(null);
    } catch (e) {
      return const Left(CacheFailure('Failed to load user'));
    }
  }

  @override
  Future<Either<Failure, User>> login(String email, String password) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // For demo purposes, create a demo user
      final user = User(
        id: 'demo_user_001',
        name: 'Alex Johnson',
        email: email,
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
        studyStreak: 7,
        totalCardsStudied: 142,
        preferences: const {
          'enableNotifications': true,
          'studyReminders': true,
          'soundEffects': true,
          'hapticFeedback': true,
          'autoPlayAudio': false,
        },
      );
      
      await saveUser(user);
      return Right(user);
    } catch (e) {
      return const Left(ServerFailure('Login failed'));
    }
  }

  @override
  Future<Either<Failure, User>> register(String name, String email, String password) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      final user = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        email: email,
        joinDate: DateTime.now(),
      );
      
      await saveUser(user);
      return Right(user);
    } catch (e) {
      return const Left(ServerFailure('Registration failed'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      return const Right(null);
    } catch (e) {
      return const Left(CacheFailure('Logout failed'));
    }
  }

  @override
  Future<Either<Failure, User>> updateProfile(User user) async {
    try {
      await saveUser(user);
      return Right(user);
    } catch (e) {
      return const Left(CacheFailure('Failed to update profile'));
    }
  }

  @override
  Future<Either<Failure, User>> updatePreferences(String userId, Map<String, dynamic> preferences) async {
    try {
      final currentUserResult = await getCurrentUser();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          if (currentUser == null || currentUser.id != userId) {
            return const Left(NotFoundFailure('User not found'));
          }
          
          final updatedUser = currentUser.updatePreferences(preferences);
          await saveUser(updatedUser);
          return Right(updatedUser);
        },
      );
    } catch (e) {
      return const Left(CacheFailure('Failed to update preferences'));
    }
  }

  @override
  Future<Either<Failure, User>> incrementStudyStats(String userId, {int cardsStudied = 0, bool maintainStreak = false}) async {
    try {
      final currentUserResult = await getCurrentUser();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          if (currentUser == null || currentUser.id != userId) {
            return const Left(NotFoundFailure('User not found'));
          }
          
          final updatedUser = currentUser.incrementStudyStats(
            cardsStudied: cardsStudied,
            maintainStreak: maintainStreak,
          );
          await saveUser(updatedUser);
          return Right(updatedUser);
        },
      );
    } catch (e) {
      return const Left(CacheFailure('Failed to update study stats'));
    }
  }

  @override
  Future<Either<Failure, void>> saveUser(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMap = {
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'avatarUrl': user.avatarUrl,
        'joinDate': user.joinDate.toIso8601String(),
        'studyStreak': user.studyStreak,
        'totalCardsStudied': user.totalCardsStudied,
        'preferences': user.preferences,
      };
      await prefs.setString(_userKey, json.encode(userMap));
      return const Right(null);
    } catch (e) {
      return const Left(CacheFailure('Failed to save user'));
    }
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    try {
      // Simulate API call for password reset
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real implementation, this would send an email with reset instructions
      // For demo purposes, we'll just simulate success
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure('Failed to send reset email'));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(String email, String verificationCode, String newPassword) async {
    try {
      // Simulate API call for password reset verification
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real implementation, this would verify the code and update the password
      // For demo purposes, we'll just simulate success
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure('Failed to reset password'));
    }
  }

  @override
  Future<Either<Failure, void>> verifyEmail(String email, String verificationCode) async {
    try {
      // Simulate API call for email verification
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real implementation, this would verify the email with the code
      // For demo purposes, we'll just simulate success
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure('Failed to verify email'));
    }
  }

  @override
  Future<Either<Failure, void>> resendVerificationEmail(String email) async {
    try {
      // Simulate API call for resending verification email
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real implementation, this would resend the verification email
      // For demo purposes, we'll just simulate success
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure('Failed to resend verification email'));
    }
  }
} 