import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../core/errors/failures.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/repositories/deck_repository.dart';
import '../datasources/local/deck_local_data_source.dart';
import '../models/deck_model.dart';
import '../models/flashcard_model.dart';

@Injectable(as: DeckRepository)
class DeckRepositoryImpl implements DeckRepository {
  final DeckLocalDataSource localDataSource;

  DeckRepositoryImpl(this.localDataSource);

  @override
  Future<Either<Failure, List<Deck>>> getDecks() async {
    try {
      final deckModels = await localDataSource.getDecks();
      final decks = deckModels.map((model) => model.toEntity()).toList();
      return Right(decks);
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to load decks'));
    }
  }

  @override
  Future<Either<Failure, Deck>> getDeckById(String id) async {
    try {
      final deckModel = await localDataSource.getDeckById(id);
      if (deckModel == null) {
        return const Left(NotFoundFailure('Deck not found'));
      }
      return Right(deckModel.toEntity());
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to load deck'));
    }
  }

  @override
  Future<Either<Failure, Deck>> createDeck(String name, String description) async {
    try {
      final deckModel = DeckModel.create(name: name, description: description);
      await localDataSource.saveDeck(deckModel);
      return Right(deckModel.toEntity());
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to create deck'));
    }
  }

  @override
  Future<Either<Failure, Deck>> updateDeck(Deck deck) async {
    try {
      final deckModel = DeckModel.fromEntity(deck);
      await localDataSource.saveDeck(deckModel);
      return Right(deck);
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to update deck'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteDeck(String id) async {
    try {
      await localDataSource.deleteDeck(id);
      return const Right(null);
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to delete deck'));
    }
  }

  @override
  Future<Either<Failure, Deck>> addFlashcardToDeck(String deckId, Flashcard flashcard) async {
    try {
      final deckModel = await localDataSource.getDeckById(deckId);
      if (deckModel == null) {
        return const Left(NotFoundFailure('Deck not found'));
      }

      final flashcardModel = FlashcardModel.fromEntity(flashcard);
      final updatedDeckModel = deckModel.addFlashcard(flashcardModel);
      await localDataSource.saveDeck(updatedDeckModel);
      
      return Right(updatedDeckModel.toEntity());
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to add flashcard to deck'));
    }
  }

  @override
  Future<Either<Failure, Deck>> updateFlashcardInDeck(String deckId, Flashcard flashcard) async {
    try {
      final deckModel = await localDataSource.getDeckById(deckId);
      if (deckModel == null) {
        return const Left(NotFoundFailure('Deck not found'));
      }

      final flashcardModel = FlashcardModel.fromEntity(flashcard);
      final updatedDeckModel = deckModel.updateFlashcard(flashcardModel);
      await localDataSource.saveDeck(updatedDeckModel);
      
      return Right(updatedDeckModel.toEntity());
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to update flashcard in deck'));
    }
  }

  @override
  Future<Either<Failure, Deck>> removeFlashcardFromDeck(String deckId, String flashcardId) async {
    try {
      final deckModel = await localDataSource.getDeckById(deckId);
      if (deckModel == null) {
        return const Left(NotFoundFailure('Deck not found'));
      }

      final updatedDeckModel = deckModel.removeFlashcard(flashcardId);
      await localDataSource.saveDeck(updatedDeckModel);
      
      return Right(updatedDeckModel.toEntity());
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to remove flashcard from deck'));
    }
  }

  @override
  Future<Either<Failure, List<Deck>>> searchDecks(String query) async {
    try {
      final deckModels = await localDataSource.getDecks();
      final filteredModels = deckModels.where((deck) =>
          deck.name.toLowerCase().contains(query.toLowerCase()) ||
          deck.description.toLowerCase().contains(query.toLowerCase())
      ).toList();
      
      final decks = filteredModels.map((model) => model.toEntity()).toList();
      return Right(decks);
    } catch (e) {
      if (e is Failure) {
        return Left(e);
      }
      return const Left(CacheFailure('Failed to search decks'));
    }
  }
} 