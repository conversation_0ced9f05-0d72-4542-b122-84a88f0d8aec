import 'package:dartz/dartz.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import 'dart:convert';

import '../../core/errors/failures.dart';
import '../../domain/entities/settings.dart';
import '../../domain/entities/user_preferences.dart';
import '../../domain/repositories/settings_repository.dart';

@Injectable(as: SettingsRepository)
class SettingsRepositoryImpl implements SettingsRepository {
  static const String _settingsKey = 'app_settings';
  static const String _userPreferencesKey = 'user_preferences';

  @override
  Future<Either<Failure, Settings>> getSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        final settings = Settings(
          notificationsEnabled: settingsMap['notificationsEnabled'] ?? true,
          soundEnabled: settingsMap['soundEnabled'] ?? true,
          vibrationEnabled: settingsMap['vibrationEnabled'] ?? true,
          language: settingsMap['language'] ?? 'en',
          studyReminder: settingsMap['studyReminder'] ?? 24,
          autoAdvance: settingsMap['autoAdvance'] ?? false,
          cardDisplayTime: settingsMap['cardDisplayTime'] ?? 5,
        );
        return Right(settings);
      }
      
      // Return default settings
      return const Right(Settings());
    } catch (e) {
      return const Left(CacheFailure('Failed to load settings'));
    }
  }

  @override
  Future<Either<Failure, Settings>> updateSettings(Settings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsMap = {
        'notificationsEnabled': settings.notificationsEnabled,
        'soundEnabled': settings.soundEnabled,
        'vibrationEnabled': settings.vibrationEnabled,
        'language': settings.language,
        'studyReminder': settings.studyReminder,
        'autoAdvance': settings.autoAdvance,
        'cardDisplayTime': settings.cardDisplayTime,
      };
      await prefs.setString(_settingsKey, json.encode(settingsMap));
      return Right(settings);
    } catch (e) {
      return const Left(CacheFailure('Failed to update settings'));
    }
  }

  @override
  Future<Either<Failure, void>> resetSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_settingsKey);
      await prefs.remove(_userPreferencesKey);
      return const Right(null);
    } catch (e) {
      return const Left(CacheFailure('Failed to reset settings'));
    }
  }

  @override
  Future<Either<Failure, UserPreferences>> getUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = prefs.getString(_userPreferencesKey);
      
      if (preferencesJson != null) {
        final preferencesMap = json.decode(preferencesJson) as Map<String, dynamic>;
        final userPreferences = UserPreferences(
          notificationsEnabled: preferencesMap['notificationsEnabled'] ?? true,
          soundEnabled: preferencesMap['soundEnabled'] ?? true,
          vibrationEnabled: preferencesMap['vibrationEnabled'] ?? true,
          studyReminder: preferencesMap['studyReminder'] ?? 'daily',
          dailyGoal: preferencesMap['dailyGoal'] ?? 10,
          theme: preferencesMap['theme'] ?? 'system',
          language: preferencesMap['language'] ?? 'en',
          autoPlayAudio: preferencesMap['autoPlayAudio'] ?? false,
          showHints: preferencesMap['showHints'] ?? true,
          sessionDuration: preferencesMap['sessionDuration'] ?? 30,
        );
        return Right(userPreferences);
      }
      
      // Return default preferences
      return const Right(UserPreferences());
    } catch (e) {
      return const Left(CacheFailure('Failed to load user preferences'));
    }
  }

  @override
  Future<Either<Failure, UserPreferences>> updateUserPreferences(UserPreferences preferences) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesMap = {
        'notificationsEnabled': preferences.notificationsEnabled,
        'soundEnabled': preferences.soundEnabled,
        'vibrationEnabled': preferences.vibrationEnabled,
        'studyReminder': preferences.studyReminder,
        'dailyGoal': preferences.dailyGoal,
        'theme': preferences.theme,
        'language': preferences.language,
        'autoPlayAudio': preferences.autoPlayAudio,
        'showHints': preferences.showHints,
        'sessionDuration': preferences.sessionDuration,
      };
      await prefs.setString(_userPreferencesKey, json.encode(preferencesMap));
      return Right(preferences);
    } catch (e) {
      return const Left(CacheFailure('Failed to update user preferences'));
    }
  }
} 