// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'deck_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeckModel _$DeckModelFromJson(Map<String, dynamic> json) => DeckModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      flashcards: (json['flashcards'] as List<dynamic>)
          .map((e) => FlashcardModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastStudied: json['lastStudied'] == null
          ? null
          : DateTime.parse(json['lastStudied'] as String),
    );

Map<String, dynamic> _$DeckModelToJson(DeckModel instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'flashcards': instance.flashcards,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastStudied': instance.lastStudied?.toIso8601String(),
    };
