// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flashcard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FlashcardModel _$FlashcardModelFromJson(Map<String, dynamic> json) =>
    FlashcardModel(
      id: json['id'] as String,
      front: json['front'] as String,
      back: json['back'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastReviewed: json['lastReviewed'] == null
          ? null
          : DateTime.parse(json['lastReviewed'] as String),
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
      difficulty: (json['difficulty'] as num?)?.toDouble() ?? 0.5,
    );

Map<String, dynamic> _$FlashcardModelToJson(FlashcardModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'front': instance.front,
      'back': instance.back,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastReviewed': instance.lastReviewed?.toIso8601String(),
      'reviewCount': instance.reviewCount,
      'difficulty': instance.difficulty,
    };
