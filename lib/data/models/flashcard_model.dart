import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/flashcard.dart';

part 'flashcard_model.g.dart';

@JsonSerializable()
class FlashcardModel extends Equatable {
  final String id;
  final String front;
  final String back;
  final DateTime createdAt;
  final DateTime? lastReviewed;
  final int reviewCount;
  final double difficulty; // 0.0 to 1.0, higher means more difficult

  const FlashcardModel({
    required this.id,
    required this.front,
    required this.back,
    required this.createdAt,
    this.lastReviewed,
    this.reviewCount = 0,
    this.difficulty = 0.5,
  });

  factory FlashcardModel.create({
    required String front,
    required String back,
  }) {
    return FlashcardModel(
      id: const Uuid().v4(),
      front: front,
      back: back,
      createdAt: DateTime.now(),
    );
  }

  factory FlashcardModel.fromJson(Map<String, dynamic> json) => _$FlashcardModelFromJson(json);
  Map<String, dynamic> toJson() => _$FlashcardModelToJson(this);

  // Convert from domain entity to data model
  factory FlashcardModel.fromEntity(Flashcard entity) {
    return FlashcardModel(
      id: entity.id,
      front: entity.front,
      back: entity.back,
      createdAt: entity.createdAt,
      lastReviewed: entity.lastReviewed,
      reviewCount: entity.reviewCount,
      difficulty: entity.difficulty,
    );
  }

  // Convert from data model to domain entity
  Flashcard toEntity() {
    return Flashcard(
      id: id,
      front: front,
      back: back,
      createdAt: createdAt,
      lastReviewed: lastReviewed,
      reviewCount: reviewCount,
      difficulty: difficulty,
    );
  }

  FlashcardModel copyWith({
    String? id,
    String? front,
    String? back,
    DateTime? createdAt,
    DateTime? lastReviewed,
    int? reviewCount,
    double? difficulty,
  }) {
    return FlashcardModel(
      id: id ?? this.id,
      front: front ?? this.front,
      back: back ?? this.back,
      createdAt: createdAt ?? this.createdAt,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      reviewCount: reviewCount ?? this.reviewCount,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  @override
  List<Object?> get props => [
    id,
    front,
    back,
    createdAt,
    lastReviewed,
    reviewCount,
    difficulty,
  ];
} 