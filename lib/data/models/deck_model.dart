import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/deck.dart';
import 'flashcard_model.dart';

part 'deck_model.g.dart';

@JsonSerializable()
class DeckModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final List<FlashcardModel> flashcards;
  final DateTime createdAt;
  final DateTime? lastStudied;

  const DeckModel({
    required this.id,
    required this.name,
    required this.description,
    required this.flashcards,
    required this.createdAt,
    this.lastStudied,
  });

  factory DeckModel.create({
    required String name,
    required String description,
  }) {
    return DeckModel(
      id: const Uuid().v4(),
      name: name,
      description: description,
      flashcards: [],
      createdAt: DateTime.now(),
    );
  }

  factory DeckModel.fromJson(Map<String, dynamic> json) => _$DeckModelFromJson(json);
  Map<String, dynamic> toJson() => _$DeckModelToJson(this);

  // Convert from domain entity to data model
  factory DeckModel.fromEntity(Deck entity) {
    return DeckModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      flashcards: entity.flashcards.map((flashcard) => FlashcardModel.fromEntity(flashcard)).toList(),
      createdAt: entity.createdAt,
      lastStudied: entity.lastStudied,
    );
  }

  // Convert from data model to domain entity
  Deck toEntity() {
    return Deck(
      id: id,
      name: name,
      description: description,
      flashcards: flashcards.map((flashcard) => flashcard.toEntity()).toList(),
      createdAt: createdAt,
      lastStudied: lastStudied,
    );
  }

  DeckModel copyWith({
    String? id,
    String? name,
    String? description,
    List<FlashcardModel>? flashcards,
    DateTime? createdAt,
    DateTime? lastStudied,
  }) {
    return DeckModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      flashcards: flashcards ?? this.flashcards,
      createdAt: createdAt ?? this.createdAt,
      lastStudied: lastStudied ?? this.lastStudied,
    );
  }

  DeckModel addFlashcard(FlashcardModel flashcard) {
    return copyWith(
      flashcards: [...flashcards, flashcard],
    );
  }

  DeckModel updateFlashcard(FlashcardModel updatedCard) {
    final index = flashcards.indexWhere((card) => card.id == updatedCard.id);
    if (index == -1) return this;
    
    final newFlashcards = [...flashcards];
    newFlashcards[index] = updatedCard;
    
    return copyWith(flashcards: newFlashcards);
  }

  DeckModel removeFlashcard(String flashcardId) {
    return copyWith(
      flashcards: flashcards.where((card) => card.id != flashcardId).toList(),
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    flashcards,
    createdAt,
    lastStudied,
  ];
} 