import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'dart:developer' as developer;

import 'core/di/injection_container.dart';
import 'core/services/navigation_service.dart';
import 'core/theme/app_theme.dart';
import 'presentation/viewmodels/theme_view_model.dart';
import 'presentation/viewmodels/user_view_model.dart';
import 'presentation/viewmodels/deck_view_model.dart';
import 'presentation/views/main_navigation_view_shadcn.dart';
import 'presentation/views/auth/login_view_shadcn.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize MVVM dependencies
    await initializeDependencies();

    // Initialize ViewModels
    final themeViewModel = getIt<ThemeViewModel>();
    final userViewModel = getIt<UserViewModel>();
    final deckViewModel = getIt<DeckViewModel>();
    
    await themeViewModel.loadTheme();
    await userViewModel.loadUserState();
    await deckViewModel.loadDecks();

    runApp(FlashCardsApp(
      themeViewModel: themeViewModel,
      userViewModel: userViewModel,
      deckViewModel: deckViewModel,
    ));
  } catch (e, stackTrace) {
    developer.log(
      'Error during app initialization',
      name: 'main',
      error: e,
      stackTrace: stackTrace,
    );
    
    // Run a fallback app
    runApp(ErrorApp(error: e.toString()));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error App',
      home: Material(
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const Text(
                  'App Initialization Error:',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text(
                  error,
                  style: const TextStyle(color: Color(0xFFD32F2F)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class FlashCardsApp extends StatelessWidget {
  final ThemeViewModel themeViewModel;
  final UserViewModel userViewModel;
  final DeckViewModel deckViewModel;

  const FlashCardsApp({
    super.key,
    required this.themeViewModel,
    required this.userViewModel,
    required this.deckViewModel,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // MVVM ViewModels
        ChangeNotifierProvider.value(value: themeViewModel),
        ChangeNotifierProvider.value(value: userViewModel),
        ChangeNotifierProvider.value(value: deckViewModel),
      ],
      child: Consumer3<ThemeViewModel, UserViewModel, DeckViewModel>(
        builder: (context, themeViewModel, userViewModel, deckViewModel, _) {
          return DynamicColorBuilder(
            builder: (lightDynamic, darkDynamic) {
              return MaterialApp(
                title: 'FlashCards Pro',
                debugShowCheckedModeBanner: false,
                navigatorKey: getIt<NavigationService>().navigatorKey,

                // Use our theme configuration with dynamic colors
                theme: _buildTheme(lightDynamic, false),
                darkTheme: _buildTheme(darkDynamic, true),
                themeMode: themeViewModel.themeMode,

                home: userViewModel.isAuthenticated
                    ? const MainNavigationViewShadcn()
                    : const LoginViewShadcn(),
              );
            },
          );
        },
      ),
    );
  }

  ThemeData _buildTheme(ColorScheme? dynamicScheme, bool isDark) {
    final baseTheme = isDark ? AppTheme.darkTheme : AppTheme.lightTheme;

    if (dynamicScheme != null) {
      // Create a hybrid theme that uses dynamic colors but preserves our design language
      return baseTheme.copyWith(
        colorScheme: dynamicScheme,
        textTheme: isDark ? AppTheme.darkTextTheme : AppTheme.lightTextTheme,
      );
    }

    return baseTheme;
  }
}
