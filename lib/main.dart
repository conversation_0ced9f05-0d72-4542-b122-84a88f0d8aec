import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'dart:developer' as developer;

import 'core/di/injection_container.dart';
import 'core/services/navigation_service.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/shadcn_theme.dart';
import 'presentation/viewmodels/theme_view_model.dart';
import 'presentation/viewmodels/user_view_model.dart';
import 'presentation/viewmodels/deck_view_model.dart';
import 'presentation/views/main_navigation_view_shadcn.dart';
import 'presentation/views/auth/login_view_shadcn.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize MVVM dependencies
    await initializeDependencies();

    // Initialize ViewModels
    final themeViewModel = getIt<ThemeViewModel>();
    final userViewModel = getIt<UserViewModel>();
    final deckViewModel = getIt<DeckViewModel>();
    
    await themeViewModel.loadTheme();
    await userViewModel.loadUserState();
    await deckViewModel.loadDecks();

    runApp(FlashCardsApp(
      themeViewModel: themeViewModel,
      userViewModel: userViewModel,
      deckViewModel: deckViewModel,
    ));
  } catch (e, stackTrace) {
    developer.log(
      'Error during app initialization',
      name: 'main',
      error: e,
      stackTrace: stackTrace,
    );
    
    // Run a fallback app
    runApp(ErrorApp(error: e.toString()));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error App',
      home: Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const Text(
                'App Initialization Error:',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                error,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FlashCardsApp extends StatelessWidget {
  final ThemeViewModel themeViewModel;
  final UserViewModel userViewModel;
  final DeckViewModel deckViewModel;

  const FlashCardsApp({
    super.key,
    required this.themeViewModel,
    required this.userViewModel,
    required this.deckViewModel,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // MVVM ViewModels
        ChangeNotifierProvider.value(value: themeViewModel),
        ChangeNotifierProvider.value(value: userViewModel),
        ChangeNotifierProvider.value(value: deckViewModel),
      ],
      child: Consumer3<ThemeViewModel, UserViewModel, DeckViewModel>(
        builder: (context, themeViewModel, userViewModel, deckViewModel, _) {
          return DynamicColorBuilder(
            builder: (lightDynamic, darkDynamic) {
              return ShadcnApp(
                title: 'FlashCards Pro',
                debugShowCheckedModeBanner: false,
                navigatorKey: getIt<NavigationService>().navigatorKey,

                // Use our shadcn theme configuration with dynamic colors
                theme: _buildShadcnTheme(lightDynamic, false),
                darkTheme: _buildShadcnTheme(darkDynamic, true),
                themeMode: themeViewModel.themeMode,

                home: userViewModel.isAuthenticated
                    ? const MainNavigationViewShadcn()
                    : const LoginViewShadcn(),

                // Smooth page transitions
                builder: (context, child) {
                  return AnimatedTheme(
                    duration: const Duration(milliseconds: 200),
                    data: Theme.of(context),
                    child: child!,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  ThemeData _buildShadcnTheme(ColorScheme? dynamicScheme, bool isDark) {
    final baseShadcnTheme = isDark ? ShadcnThemeConfig.darkTheme : ShadcnThemeConfig.lightTheme;

    if (dynamicScheme != null) {
      // Create a hybrid theme that uses dynamic colors but preserves our design language
      return baseShadcnTheme.copyWith(
        colorScheme: dynamicScheme,
        textTheme: isDark ? AppTheme.darkTextTheme : AppTheme.lightTextTheme,
        // Preserve our sophisticated component themes
        appBarTheme: isDark ? AppTheme.darkAppBarTheme : AppTheme.lightAppBarTheme,
        cardTheme: isDark ? AppTheme.darkCardTheme : AppTheme.lightCardTheme,
        elevatedButtonTheme: isDark ? AppTheme.darkElevatedButtonTheme : AppTheme.lightElevatedButtonTheme,
        inputDecorationTheme: ShadcnThemeConfig.inputDecorationTheme(dynamicScheme),
        bottomNavigationBarTheme: isDark ? AppTheme.darkBottomNavigationBarTheme : AppTheme.lightBottomNavigationBarTheme,
      );
    }

    return baseShadcnTheme;
  }
}
