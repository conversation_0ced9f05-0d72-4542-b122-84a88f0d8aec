import 'package:flutter/material.dart' hide ThemeData, Scaffold, ColorScheme, ThemeMode, Colors, TextField;
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'dart:developer' as developer;

// Removed problematic imports to create a minimal working version

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize MVVM dependencies
    await initializeDependencies();

    runApp(const FlashCardsApp());
  } catch (e, stackTrace) {
    developer.log(
      'Error during app initialization',
      name: 'main',
      error: e,
      stackTrace: stackTrace,
    );

    // Run a fallback app
    runApp(ErrorApp(error: e.toString()));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error App',
      home: Material(
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const Text(
                  'App Initialization Error:',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text(
                  error,
                  style: const TextStyle(color: Color(0xFFD32F2F)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class FlashCardsApp extends StatelessWidget {
  const FlashCardsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // MVVM ViewModels
        ChangeNotifierProvider(
          create: (_) => getIt<ThemeViewModel>()..loadTheme(),
        ),
        ChangeNotifierProvider(
          create: (_) => getIt<UserViewModel>()..loadUserState(),
        ),
        ChangeNotifierProvider(
          create: (_) => getIt<DeckViewModel>()..loadDecks(),
        ),
      ],
      child: Consumer3<ThemeViewModel, UserViewModel, DeckViewModel>(
        builder: (context, themeViewModel, userViewModel, deckViewModel, _) {
          return DynamicColorBuilder(
            builder: (lightDynamic, darkDynamic) {
              return ShadcnApp(
                title: 'FlashCards Pro',
                debugShowCheckedModeBanner: false,
                navigatorKey: getIt<NavigationService>().navigatorKey,

                // Use shadcn theme configuration
                theme: _buildShadcnTheme(themeViewModel.themeMode),

                home: const WelcomePage(),
              );
            },
          );
        },
      ),
    );
  }

  ThemeData _buildShadcnTheme(dynamic themeMode) {
    // Use shadcn's built-in color schemes
    return ThemeData(
      colorScheme: ColorSchemes.lightZinc(),
      radius: 0.7, // Rounded corners
    );
  }
}

class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF9333EA),
              const Color(0xFF7C3AED),
              const Color(0xFF6366F1),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Icon/Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: const Color(0x33FFFFFF), // white with 0.2 opacity
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.school_rounded,
                    size: 60,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 40),

                // App Title
                const Text(
                  'FlashCards Pro',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 16),

                // Subtitle
                Text(
                  'Master any subject with intelligent flashcards',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color(0xE6FFFFFF), // white with 0.9 opacity
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 60),

                // Get Started Button
                SizedBox(
                  width: double.infinity,
                  child: PrimaryButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => const SimpleLoginPage(),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        'Get Started',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Demo Account Info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0x1AFFFFFF), // white with 0.1 opacity
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0x33FFFFFF), // white with 0.2 opacity
                    ),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Demo Account',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xE6FFFFFF), // white with 0.9 opacity
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Email: <EMAIL>\nPassword: demo123',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xCCFFFFFF), // white with 0.8 opacity
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SimpleLoginPage extends StatefulWidget {
  const SimpleLoginPage({super.key});

  @override
  State<SimpleLoginPage> createState() => _SimpleLoginPageState();
}

class _SimpleLoginPageState extends State<SimpleLoginPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate login process
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    // Navigate to main app
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const MainAppPage(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF9333EA),
              Color(0xFF7C3AED),
              Color(0xFF6366F1),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Back button
                Align(
                  alignment: Alignment.topLeft,
                  child: GhostButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Icon(Icons.arrow_back, color: Color(0xFFFFFFFF)),
                  ),
                ),
                const Spacer(),

                // Login Form
                const Text(
                  'Welcome Back',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 40),

                // Email Field
                TextField(
                  controller: _emailController,
                  placeholder: const Text('Email'),
                ),
                const SizedBox(height: 16),

                // Password Field
                TextField(
                  controller: _passwordController,
                  placeholder: const Text('Password'),
                  obscureText: true,
                ),
                const SizedBox(height: 32),

                // Login Button
                SizedBox(
                  width: double.infinity,
                  child: PrimaryButton(
                    onPressed: _isLoading ? null : _handleLogin,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: _isLoading
                          ? const Progress()
                          : const Text(
                              'Sign In',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Demo credentials
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0x1AFFFFFF),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0x33FFFFFF)),
                  ),
                  child: const Column(
                    children: [
                      Text(
                        'Demo Credentials',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xE6FFFFFF),
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Email: <EMAIL>\nPassword: demo123',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xCCFFFFFF),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MainAppPage extends StatelessWidget {
  const MainAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF06B6D4),
              Color(0xFF3B82F6),
              Color(0xFF1E40AF),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Success Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: const Color(0x33FFFFFF),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.check_circle_rounded,
                    size: 60,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 40),

                // Success Message
                const Text(
                  'Welcome to FlashCards Pro!',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFFFFFF),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                const Text(
                  'You have successfully logged in.\nThe full app functionality is now available!',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color(0xE6FFFFFF),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 60),

                // Continue Button
                SizedBox(
                  width: double.infinity,
                  child: PrimaryButton(
                    onPressed: () {
                      // Here you would normally navigate to the main navigation
                      // For now, let's just show a message
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Main app navigation would start here!'),
                          backgroundColor: Color(0xFF10B981),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        'Continue to App',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Logout Button
                SizedBox(
                  width: double.infinity,
                  child: GhostButton(
                    onPressed: () {
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (context) => const WelcomePage(),
                        ),
                        (route) => false,
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        'Logout',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xE6FFFFFF),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
