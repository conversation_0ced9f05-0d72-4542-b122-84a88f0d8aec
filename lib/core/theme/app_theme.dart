import 'package:flutter/material.dart';

class AppTheme {
  // Ultra-sophisticated dark-first color scheme with deeper, richer gradients
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF6366F1), // Elegant indigo
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFE0E7FF),
    onPrimaryContainer: Color(0xFF312E81),
    secondary: Color(0xFF8B5CF6), // Rich purple
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFF3E8FF),
    onSecondaryContainer: Color(0xFF581C87),
    tertiary: Color(0xFF06B6D4), // Sophisticated cyan
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFCFFAFE),
    onTertiaryContainer: Color(0xFF164E63),
    error: Color(0xFFEF4444),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFEE2E2),
    onErrorContainer: Color(0xFF7F1D1D),
    surface: Color(0xFFFAFAFA),
    onSurface: Color(0xFF0F172A),
    surfaceContainerHighest: Color(0xFFF1F5F9),
    outline: Color(0xFFE2E8F0),
    outlineVariant: Color(0xFFF1F5F9),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF0F172A),
    onInverseSurface: Color(0xFFF8FAFC),
    inversePrimary: Color(0xFF818CF8),
    surfaceTint: Color(0xFF6366F1),
  );

  // Ultra-sophisticated dark theme - even darker, more minimal and beautiful
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF9333EA), // Vibrant purple
    onPrimary: Color(0xFF050505),
    primaryContainer: Color(0xFF7C3AED),
    onPrimaryContainer: Color(0xFFE0E7FF),
    secondary: Color(0xFF06B6D4), // Electric cyan
    onSecondary: Color(0xFF050505),
    secondaryContainer: Color(0xFF0891B2),
    onSecondaryContainer: Color(0xFFCFFAFE),
    tertiary: Color(0xFFFFB800), // Golden amber
    onTertiary: Color(0xFF050505),
    tertiaryContainer: Color(0xFFD97706),
    onTertiaryContainer: Color(0xFFFEF3C7),
    error: Color(0xFFFF6B6B),
    onError: Color(0xFF050505),
    errorContainer: Color(0xFFDC2626),
    onErrorContainer: Color(0xFFFEE2E2),
    surface: Color(0xFF0A0A0F), // Ultra dark navy - even darker
    onSurface: Color(0xFFF8FAFC),
    surfaceContainerHighest: Color(0xFF12121A), // Slightly lighter dark
    outline: Color(0xFF2A2A3A),
    outlineVariant: Color(0xFF3A3A4A),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFF8FAFC),
    onInverseSurface: Color(0xFF0A0A0F),
    inversePrimary: Color(0xFF6366F1),
    surfaceTint: Color(0xFF9333EA),
  );

  // Enhanced gradient definitions with deeper, more sophisticated colors
  static const List<Color> primaryGradient = [
    Color(0xFF9333EA), // Vibrant purple
    Color(0xFF7C3AED), // Deep purple
    Color(0xFF6366F1), // Royal indigo
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF06B6D4), // Electric cyan
    Color(0xFF3B82F6), // Royal blue
    Color(0xFF1E40AF), // Deep blue
  ];

  static const List<Color> accentGradient = [
    Color(0xFFFFB800), // Golden amber
    Color(0xFFFF8A00), // Orange
    Color(0xFFEF4444), // Red accent
  ];

  static const List<Color> surfaceGradient = [
    Color(0xFF0A0A0F), // Ultra dark
    Color(0xFF12121A), // Dark grey
    Color(0xFF1A1A2E), // Dark blue-grey
    Color(0xFF16213E), // Deep navy
  ];

  static const List<Color> heroGradient = [
    Color(0xFF9333EA),
    Color(0xFF06B6D4),
    Color(0xFFFFB800),
    Color(0xFFEF4444),
  ];

  static const List<Color> successGradient = [
    Color(0xFF10B981), // Emerald green
    Color(0xFF059669), // Green
    Color(0xFF047857), // Dark green
  ];

  static const List<Color> warningGradient = [
    Color(0xFFF59E0B), // Amber
    Color(0xFFEAB308), // Yellow
    Color(0xFFCA8A04), // Dark amber
  ];

  // Minimal, sophisticated typography with lighter weights
  static TextTheme get _baseTextTheme => const TextTheme(
    displayLarge: TextStyle(
      fontSize: 48,
      fontWeight: FontWeight.w100, // Ultra light
      letterSpacing: -2.0,
      height: 1.1,
    ),
    displayMedium: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w200, // Extra light
      letterSpacing: -1.0,
      height: 1.15,
    ),
    displaySmall: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w300, // Light
      letterSpacing: -0.5,
      height: 1.2,
    ),
    headlineLarge: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w300, // Light
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 0.1,
      height: 1.3,
    ),
    headlineSmall: TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 0.1,
      height: 1.35,
    ),
    titleLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500, // Medium
      letterSpacing: 0.1,
      height: 1.4,
    ),
    titleMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 0.1,
      height: 1.4,
    ),
    titleSmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 0.1,
      height: 1.4,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w300, // Light
      letterSpacing: 0.3,
      height: 1.6,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w300, // Light
      letterSpacing: 0.2,
      height: 1.55,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w300, // Light
      letterSpacing: 0.3,
      height: 1.5,
    ),
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 1.0,
      height: 1.4,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 1.0,
      height: 1.35,
    ),
    labelSmall: TextStyle(
      fontSize: 10,
      fontWeight: FontWeight.w400, // Normal
      letterSpacing: 1.2,
      height: 1.35,
    ),
  );

  static TextTheme get lightTextTheme => _baseTextTheme.apply(
        bodyColor: lightColorScheme.onSurface,
        displayColor: lightColorScheme.onSurface,
        fontFamily: 'SF Pro Display',
      );

  static TextTheme get darkTextTheme => _baseTextTheme.apply(
        bodyColor: darkColorScheme.onSurface,
        displayColor: darkColorScheme.onSurface,
        fontFamily: 'SF Pro Display',
      );

  // Ultra-minimal app bar themes with sophisticated blur effects
  static AppBarTheme get lightAppBarTheme => AppBarTheme(
        elevation: 0,
        centerTitle: false,
        backgroundColor: Colors.transparent,
        foregroundColor: lightColorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.transparent,
        titleTextStyle: lightTextTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.w200, // Even lighter
        ),
        iconTheme: IconThemeData(
          color: lightColorScheme.onSurface,
          size: 20, // Smaller, more minimal
        ),
        actionsIconTheme: IconThemeData(
          color: lightColorScheme.onSurface,
          size: 20,
        ),
      );

  static AppBarTheme get darkAppBarTheme => AppBarTheme(
        elevation: 0,
        centerTitle: false,
        backgroundColor: Colors.transparent,
        foregroundColor: darkColorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.transparent,
        titleTextStyle: darkTextTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.w200, // Even lighter
        ),
        iconTheme: IconThemeData(
          color: darkColorScheme.onSurface,
          size: 20, // Smaller, more minimal
        ),
        actionsIconTheme: IconThemeData(
          color: darkColorScheme.onSurface,
          size: 20,
        ),
      );

  // Enhanced card themes with sophisticated glass morphism
  static CardThemeData get lightCardTheme => CardThemeData(
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32), // More rounded
        ),
        color: lightColorScheme.surface.withValues(alpha: 0.4), // More transparent
        surfaceTintColor: Colors.transparent,
        margin: const EdgeInsets.all(8),
      );

  static CardThemeData get darkCardTheme => CardThemeData(
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32), // More rounded
        ),
        color: darkColorScheme.surfaceContainerHighest.withValues(alpha: 0.2), // More transparent
        surfaceTintColor: Colors.transparent,
        margin: const EdgeInsets.all(8),
      );

  // Beautiful button themes with enhanced micro animations
  static ElevatedButtonThemeData get lightElevatedButtonTheme =>
      ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          foregroundColor: lightColorScheme.onPrimary,
          backgroundColor: lightColorScheme.primary,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // More rounded
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
          textStyle: lightTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w400, // Lighter
          ),
          animationDuration: const Duration(milliseconds: 200), // Faster
        ),
      );

  static ElevatedButtonThemeData get darkElevatedButtonTheme =>
      ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          foregroundColor: darkColorScheme.onPrimary,
          backgroundColor: darkColorScheme.primary,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // More rounded
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
          textStyle: darkTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w400, // Lighter
          ),
          animationDuration: const Duration(milliseconds: 200), // Faster
        ),
      );

  // Sophisticated input decoration themes
  static InputDecorationTheme get lightInputDecorationTheme =>
      InputDecorationTheme(
        filled: true,
        fillColor: lightColorScheme.surfaceContainerHighest.withValues(alpha: 0.3), // More transparent
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24), // More rounded
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: lightColorScheme.primary,
            width: 1.0, // Thinner
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: lightColorScheme.error,
            width: 1.0,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        hintStyle: lightTextTheme.bodyMedium?.copyWith(
          color: lightColorScheme.outline,
          fontWeight: FontWeight.w300,
        ),
        labelStyle: lightTextTheme.bodyMedium?.copyWith(
          color: lightColorScheme.outline,
          fontWeight: FontWeight.w300,
        ),
      );

  static InputDecorationTheme get darkInputDecorationTheme =>
      InputDecorationTheme(
        filled: true,
        fillColor: darkColorScheme.surfaceContainerHighest.withValues(alpha: 0.15), // More transparent
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24), // More rounded
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: darkColorScheme.primary,
            width: 1.0, // Thinner
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: darkColorScheme.error,
            width: 1.0,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        hintStyle: darkTextTheme.bodyMedium?.copyWith(
          color: darkColorScheme.outline,
          fontWeight: FontWeight.w300,
        ),
        labelStyle: darkTextTheme.bodyMedium?.copyWith(
          color: darkColorScheme.outline,
          fontWeight: FontWeight.w300,
        ),
      );

  // Minimal navigation bar theme - completely transparent
  static BottomNavigationBarThemeData get lightBottomNavigationBarTheme =>
      BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        selectedItemColor: lightColorScheme.primary,
        unselectedItemColor: lightColorScheme.outline,
        elevation: 0,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        selectedLabelStyle: lightTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w400,
        ),
        unselectedLabelStyle: lightTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w300,
        ),
      );

  static BottomNavigationBarThemeData get darkBottomNavigationBarTheme =>
      BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        selectedItemColor: darkColorScheme.primary,
        unselectedItemColor: darkColorScheme.outline,
        elevation: 0,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        selectedLabelStyle: darkTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w400,
        ),
        unselectedLabelStyle: darkTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w300,
        ),
      );

  // Main theme builders
  static ThemeData get light => ThemeData(
        useMaterial3: true,
        colorScheme: lightColorScheme,
        textTheme: lightTextTheme,
        appBarTheme: lightAppBarTheme,
        cardTheme: lightCardTheme,
        elevatedButtonTheme: lightElevatedButtonTheme,
        inputDecorationTheme: lightInputDecorationTheme,
        bottomNavigationBarTheme: lightBottomNavigationBarTheme,
        scaffoldBackgroundColor: lightColorScheme.surface,
        dividerColor: lightColorScheme.outline.withValues(alpha: 0.05), // More subtle
        splashColor: lightColorScheme.primary.withValues(alpha: 0.03), // More subtle
        highlightColor: lightColorScheme.primary.withValues(alpha: 0.01), // More subtle
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: lightColorScheme.primary,
          selectionColor: Colors.transparent, // Remove rectangular highlights
          selectionHandleColor: lightColorScheme.primary.withValues(alpha: 0.3),
        ),
        visualDensity: VisualDensity.adaptivePlatformDensity,
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
            TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      );

  static ThemeData get dark => ThemeData(
        useMaterial3: true,
        colorScheme: darkColorScheme,
        textTheme: darkTextTheme,
        appBarTheme: darkAppBarTheme,
        cardTheme: darkCardTheme,
        elevatedButtonTheme: darkElevatedButtonTheme,
        inputDecorationTheme: darkInputDecorationTheme,
        bottomNavigationBarTheme: darkBottomNavigationBarTheme,
        scaffoldBackgroundColor: darkColorScheme.surface,
        dividerColor: darkColorScheme.outline.withValues(alpha: 0.05), // More subtle
        splashColor: darkColorScheme.primary.withValues(alpha: 0.03), // More subtle
        highlightColor: darkColorScheme.primary.withValues(alpha: 0.01), // More subtle
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: darkColorScheme.primary,
          selectionColor: Colors.transparent, // Remove rectangular highlights
          selectionHandleColor: darkColorScheme.primary.withValues(alpha: 0.3),
        ),
        visualDensity: VisualDensity.adaptivePlatformDensity,
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
            TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      );

  // Enhanced gradient utilities with more sophisticated options
  static LinearGradient get primaryLinearGradient => LinearGradient(
        colors: primaryGradient,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 0.5, 1.0],
      );

  static LinearGradient get secondaryLinearGradient => LinearGradient(
        colors: secondaryGradient,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 0.5, 1.0],
      );

  static LinearGradient get accentLinearGradient => LinearGradient(
        colors: accentGradient,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 0.5, 1.0],
      );

  static LinearGradient get surfaceLinearGradient => LinearGradient(
        colors: surfaceGradient,
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        stops: const [0.0, 0.3, 0.7, 1.0],
      );

  static LinearGradient get heroLinearGradient => LinearGradient(
        colors: heroGradient,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 0.33, 0.66, 1.0],
      );

  // Enhanced shadow utilities with more sophisticated options
  static List<BoxShadow> get subtleShadow => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.03), // More subtle
          blurRadius: 24,
          offset: const Offset(0, 8),
          spreadRadius: 0,
        ),
      ];

  static List<BoxShadow> get softShadow => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.06), // More subtle
          blurRadius: 32,
          offset: const Offset(0, 12),
          spreadRadius: 0,
        ),
      ];

  static List<BoxShadow> get mediumShadow => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.08), // More subtle
          blurRadius: 40,
          offset: const Offset(0, 16),
          spreadRadius: 0,
        ),
      ];

  static List<BoxShadow> get glowShadow => [
        BoxShadow(
          color: primaryGradient[0].withValues(alpha: 0.2), // More subtle
          blurRadius: 32,
          offset: const Offset(0, 0),
          spreadRadius: 0,
        ),
      ];

  static List<BoxShadow> get heroGlowShadow => [
        BoxShadow(
          color: primaryGradient[0].withValues(alpha: 0.15),
          blurRadius: 40,
          offset: const Offset(0, 8),
          spreadRadius: 0,
        ),
        BoxShadow(
          color: secondaryGradient[0].withValues(alpha: 0.1),
          blurRadius: 60,
          offset: const Offset(0, 16),
          spreadRadius: 0,
        ),
      ];

  // Enhanced animation curves for micro interactions
  static const Curve springCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeOutCubic;
  static const Curve quickCurve = Curves.easeOutQuart;
  static const Curve gentleCurve = Curves.easeOutSine;
  static const Curve sharpCurve = Curves.easeOutExpo;
  
  // Enhanced durations for consistent animations
  static const Duration microDuration = Duration(milliseconds: 150);
  static const Duration shortDuration = Duration(milliseconds: 300);
  static const Duration mediumDuration = Duration(milliseconds: 500);
  static const Duration longDuration = Duration(milliseconds: 800);
  static const Duration extraLongDuration = Duration(milliseconds: 1200);
  
  // Random gradient utility for dynamic UI elements
  static List<Color> getRandomGradient() {
    final gradients = [
      primaryGradient,
      secondaryGradient,
      accentGradient,
      successGradient,
      warningGradient,
    ];
    
    final now = DateTime.now();
    final index = now.millisecond % gradients.length;
    return gradients[index];
  }
} 