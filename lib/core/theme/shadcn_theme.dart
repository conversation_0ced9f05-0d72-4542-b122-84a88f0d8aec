import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'app_theme.dart';

class ShadcnThemeConfig {
  // Light theme configuration using shadcn ColorSchemes
  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: ColorSchemes.lightZinc().copyWith(
        // Override with our custom colors to maintain design consistency
        primary: AppTheme.lightColorScheme.primary,
        onPrimary: AppTheme.lightColorScheme.onPrimary,
        secondary: AppTheme.lightColorScheme.secondary,
        onSecondary: AppTheme.lightColorScheme.onSecondary,
        surface: AppTheme.lightColorScheme.surface,
        onSurface: AppTheme.lightColorScheme.onSurface,
        error: AppTheme.lightColorScheme.error,
        onError: AppTheme.lightColorScheme.onError,
      ),
      radius: 0.75, // Matches our rounded design (24px radius = 0.75 * 32)
      density: ThemeDensity.comfortable,
      scaling: ThemeScaling.normal,
      textTheme: AppTheme.lightTextTheme,
      // Preserve our sophisticated visual design
      useMaterial3: true,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  // Dark theme configuration using shadcn ColorSchemes
  static ThemeData get darkTheme {
    return ThemeData(
      colorScheme: ColorSchemes.darkZinc().copyWith(
        // Override with our custom colors to maintain design consistency
        primary: AppTheme.darkColorScheme.primary,
        onPrimary: AppTheme.darkColorScheme.onPrimary,
        secondary: AppTheme.darkColorScheme.secondary,
        onSecondary: AppTheme.darkColorScheme.onSecondary,
        surface: AppTheme.darkColorScheme.surface,
        onSurface: AppTheme.darkColorScheme.onSurface,
        error: AppTheme.darkColorScheme.error,
        onError: AppTheme.darkColorScheme.onError,
      ),
      radius: 0.75, // Matches our rounded design
      density: ThemeDensity.comfortable,
      scaling: ThemeScaling.normal,
      textTheme: AppTheme.darkTextTheme,
      // Preserve our sophisticated visual design
      useMaterial3: true,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  // Input theme that matches our sophisticated input design
  static InputDecorationTheme inputDecorationTheme(ColorScheme colorScheme) => InputDecorationTheme(
    filled: true,
    fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(24),
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(24),
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(24),
      borderSide: BorderSide(
        color: colorScheme.primary,
        width: 1.0,
      ),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
  );

  // Gradient utilities for shadcn components
  static LinearGradient get primaryGradient => LinearGradient(
    colors: AppTheme.primaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    stops: const [0.0, 0.5, 1.0],
  );

  static LinearGradient get secondaryGradient => LinearGradient(
    colors: AppTheme.secondaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    stops: const [0.0, 0.5, 1.0],
  );

  static LinearGradient get successGradient => LinearGradient(
    colors: AppTheme.successGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    stops: const [0.0, 0.5, 1.0],
  );

  static LinearGradient get warningGradient => LinearGradient(
    colors: AppTheme.warningGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    stops: const [0.0, 0.5, 1.0],
  );
}
