import 'package:flutter/material.dart';

import '../../domain/entities/deck.dart';
import '../../domain/entities/study_mode.dart';
import '../../presentation/views/create_flashcard_view_shadcn.dart';
import '../../presentation/views/create_deck_view_shadcn.dart';
import '../../presentation/views/deck_detail_view_shadcn.dart';
import '../../presentation/views/main_navigation_view_shadcn.dart';
// Note: Auth views removed as they're not used in the main shadcn flow
// The main app uses LoginViewShadcn directly

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  BuildContext? get context => navigatorKey.currentContext;

  // Navigation methods
  Future<void> navigateToStudySession(Deck deck, StudyMode studyMode) async {
    if (context == null) return;
    
    // For now, show a message since StudySessionView needs to be implemented
    showSnackBar('Study session: ${studyMode.displayName} for "${deck.name}"');
  }

  Future<void> navigateToCreateFlashcard(Deck deck) async {
    if (context == null) return;

    await Navigator.push(
      context!,
      MaterialPageRoute(
        builder: (_) => CreateFlashcardViewShadcn(deckId: deck.id),
      ),
    );
  }

  Future<bool?> navigateToCreateDeck() async {
    if (context == null) return null;
    
    final result = await Navigator.push<bool>(
      context!,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CreateDeckViewShadcn(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.1),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              )),
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
    
    return result;
  }

  Future<void> navigateToDeckDetail(Deck deck) async {
    if (context == null) return;

    await Navigator.push(
      context!,
      MaterialPageRoute(
        builder: (_) => DeckDetailViewShadcn(deck: deck),
      ),
    );
  }

  void goBack() {
    if (context == null) return;
    Navigator.pop(context!);
  }

  void goBackWithResult<T>(T result) {
    if (context == null) return;
    Navigator.pop(context!, result);
  }

  Future<void> navigateToMainNavigation() async {
    if (context == null) return;
    
    await Navigator.pushReplacement(
      context!,
      MaterialPageRoute(builder: (_) => const MainNavigationViewShadcn()),
    );
  }

  // Authentication navigation methods
  // Note: These methods are deprecated as the app now uses shadcn auth flow
  // The main app handles authentication through LoginViewShadcn directly

  @Deprecated('Use LoginViewShadcn directly in main app flow')
  Future<void> navigateToWelcome() async {
    // Implementation removed - use shadcn auth flow
    throw UnimplementedError('Use LoginViewShadcn directly');
  }

  @Deprecated('Use LoginViewShadcn directly in main app flow')
  Future<void> navigateToLogin() async {
    // Implementation removed - use shadcn auth flow
    throw UnimplementedError('Use LoginViewShadcn directly');
  }

  @Deprecated('Use LoginViewShadcn directly in main app flow')
  Future<void> navigateToSignup() async {
    // Implementation removed - use shadcn auth flow
    throw UnimplementedError('Use LoginViewShadcn directly');
  }

  @Deprecated('Use LoginViewShadcn directly in main app flow')
  Future<void> navigateToForgotPassword() async {
    // Implementation removed - use shadcn auth flow
    throw UnimplementedError('Use LoginViewShadcn directly');
  }

  @Deprecated('Use LoginViewShadcn directly in main app flow')
  void navigateToLoginReplacement() {
    // Implementation removed - use shadcn auth flow
    throw UnimplementedError('Use LoginViewShadcn directly');
  }

  // Dialog methods
  Future<bool> showConfirmationDialog({
    required String title,
    required String content,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    if (context == null) return false;
    
    final result = await showDialog<bool>(
      context: context!,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  void showSnackBar(String message, {bool isError = false}) {
    if (context == null) return;
    
    ScaffoldMessenger.of(context!).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  void showErrorSnackBar(String message) {
    showSnackBar(message, isError: true);
  }
} 