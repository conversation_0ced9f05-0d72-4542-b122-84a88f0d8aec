import 'package:flutter/foundation.dart';

/// Base ViewModel that provides common functionality for all ViewModels
/// Follows MVVM principles by handling state management and error handling
abstract class BaseViewModel extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  bool _isDisposed = false;

  /// Whether the ViewModel is currently loading
  bool get isLoading => _isLoading;

  /// Current error message, if any
  String? get error => _error;

  /// Whether there's an error
  bool get hasError => _error != null;

  /// Whether the ViewModel has been disposed
  bool get isDisposed => _isDisposed;

  /// Set loading state
  void setLoading(bool loading) {
    if (_isDisposed) return;
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void setError(String? error) {
    if (_isDisposed) return;
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Clear current error
  void clearError() {
    if (_isDisposed) return;
    _error = null;
    notifyListeners();
  }

  /// Set both loading and error states
  void setState({bool? loading, String? error}) {
    if (_isDisposed) return;
    
    if (loading != null) {
      _isLoading = loading;
    }
    
    if (error != null) {
      _error = error;
      _isLoading = false;
    }
    
    notifyListeners();
  }

  /// Reset all states
  void resetState() {
    if (_isDisposed) return;
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  @override
  void notifyListeners() {
    if (!_isDisposed) {
      super.notifyListeners();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  /// Execute an async operation with automatic loading and error handling
  Future<T?> executeAsync<T>(
    Future<T> operation, {
    String? errorMessage,
    bool showLoading = true,
  }) async {
    if (showLoading) setLoading(true);
    clearError();

    try {
      final result = await operation;
      if (showLoading) setLoading(false);
      return result;
    } catch (e) {
      setError(errorMessage ?? 'An error occurred: $e');
      return null;
    }
  }

  // Template method for initialization
  Future<void> initialize() async {
    // Override in subclasses if needed
  }

  // Template method for cleanup
  Future<void> cleanup() async {
    // Override in subclasses if needed
  }
} 