// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flash_cards_app/data/datasources/local/deck_local_data_source.dart'
    as _i228;
import 'package:flash_cards_app/data/repositories/deck_repository_impl.dart'
    as _i359;
import 'package:flash_cards_app/data/repositories/flashcard_repository_impl.dart'
    as _i666;
import 'package:flash_cards_app/data/repositories/settings_repository_impl.dart'
    as _i533;
import 'package:flash_cards_app/data/repositories/study_repository_impl.dart'
    as _i1032;
import 'package:flash_cards_app/domain/repositories/deck_repository.dart'
    as _i303;
import 'package:flash_cards_app/domain/repositories/flashcard_repository.dart'
    as _i346;
import 'package:flash_cards_app/domain/repositories/settings_repository.dart'
    as _i447;
import 'package:flash_cards_app/domain/repositories/study_repository.dart'
    as _i169;
import 'package:flash_cards_app/domain/usecases/deck/create_deck.dart' as _i591;
import 'package:flash_cards_app/domain/usecases/deck/delete_deck.dart'
    as _i1024;
import 'package:flash_cards_app/domain/usecases/deck/get_deck_by_id.dart'
    as _i328;
import 'package:flash_cards_app/domain/usecases/deck/get_decks.dart' as _i120;
import 'package:flash_cards_app/domain/usecases/deck/update_deck.dart' as _i998;
import 'package:flash_cards_app/domain/usecases/flashcard/add_flashcard_to_deck.dart'
    as _i1062;
import 'package:flash_cards_app/domain/usecases/flashcard/create_flashcard.dart'
    as _i255;
import 'package:flash_cards_app/domain/usecases/flashcard/delete_flashcard.dart'
    as _i93;
import 'package:flash_cards_app/domain/usecases/flashcard/get_flashcard_by_id.dart'
    as _i404;
import 'package:flash_cards_app/domain/usecases/flashcard/update_flashcard.dart'
    as _i1034;
import 'package:flash_cards_app/domain/usecases/settings/get_user_preferences.dart'
    as _i311;
import 'package:flash_cards_app/domain/usecases/settings/update_user_preferences.dart'
    as _i400;
import 'package:flash_cards_app/domain/usecases/study/complete_study_session.dart'
    as _i70;
import 'package:flash_cards_app/domain/usecases/study/start_study_session.dart'
    as _i398;
import 'package:flash_cards_app/domain/usecases/study/update_card_performance.dart'
    as _i95;
import 'package:flash_cards_app/domain/usecases/study/update_deck_last_studied.dart'
    as _i10;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.factory<_i346.FlashcardRepository>(
        () => _i666.FlashcardRepositoryImpl());
    gh.factory<_i1034.UpdateFlashcard>(
        () => _i1034.UpdateFlashcard(gh<_i346.FlashcardRepository>()));
    gh.factory<_i1062.AddFlashcardToDeck>(
        () => _i1062.AddFlashcardToDeck(gh<_i346.FlashcardRepository>()));
    gh.factory<_i93.DeleteFlashcard>(
        () => _i93.DeleteFlashcard(gh<_i346.FlashcardRepository>()));
    gh.factory<_i255.CreateFlashcard>(
        () => _i255.CreateFlashcard(gh<_i346.FlashcardRepository>()));
    gh.factory<_i404.GetFlashcardById>(
        () => _i404.GetFlashcardById(gh<_i346.FlashcardRepository>()));
    gh.factory<_i169.StudyRepository>(() => _i1032.StudyRepositoryImpl());
    gh.factory<_i228.DeckLocalDataSource>(
        () => _i228.DeckLocalDataSourceImpl());
    gh.factory<_i447.SettingsRepository>(() => _i533.SettingsRepositoryImpl());
    gh.factory<_i303.DeckRepository>(
        () => _i359.DeckRepositoryImpl(gh<_i228.DeckLocalDataSource>()));
    gh.factory<_i70.CompleteStudySession>(
        () => _i70.CompleteStudySession(gh<_i169.StudyRepository>()));
    gh.factory<_i95.UpdateCardPerformance>(
        () => _i95.UpdateCardPerformance(gh<_i169.StudyRepository>()));
    gh.factory<_i398.StartStudySession>(
        () => _i398.StartStudySession(gh<_i169.StudyRepository>()));
    gh.factory<_i400.UpdateUserPreferences>(
        () => _i400.UpdateUserPreferences(gh<_i447.SettingsRepository>()));
    gh.factory<_i311.GetUserPreferences>(
        () => _i311.GetUserPreferences(gh<_i447.SettingsRepository>()));
    gh.factory<_i10.UpdateDeckLastStudied>(
        () => _i10.UpdateDeckLastStudied(gh<_i303.DeckRepository>()));
    gh.factory<_i998.UpdateDeck>(
        () => _i998.UpdateDeck(gh<_i303.DeckRepository>()));
    gh.factory<_i591.CreateDeck>(
        () => _i591.CreateDeck(gh<_i303.DeckRepository>()));
    gh.factory<_i328.GetDeckById>(
        () => _i328.GetDeckById(gh<_i303.DeckRepository>()));
    gh.factory<_i1024.DeleteDeck>(
        () => _i1024.DeleteDeck(gh<_i303.DeckRepository>()));
    gh.factory<_i120.GetDecks>(
        () => _i120.GetDecks(gh<_i303.DeckRepository>()));
    return this;
  }
}
