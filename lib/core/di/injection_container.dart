import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../data/repositories/user_repository_impl.dart';
import '../../data/repositories/theme_repository_impl.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/repositories/theme_repository.dart';
import '../../domain/usecases/user/get_current_user.dart';
import '../../domain/usecases/user/login_user.dart';
import '../../domain/usecases/user/register_user.dart';
import '../../domain/usecases/user/logout_user.dart';
import '../../domain/usecases/theme/get_theme_settings.dart';
import '../../domain/usecases/theme/set_theme_mode.dart';
import '../../core/services/navigation_service.dart';
import '../../presentation/viewmodels/user_view_model.dart';
import '../../presentation/viewmodels/theme_view_model.dart';
import '../../presentation/viewmodels/deck_view_model.dart';
import '../../presentation/viewmodels/deck_detail_view_model.dart';
import '../../presentation/viewmodels/home_view_model.dart';
import '../../presentation/viewmodels/create_deck_view_model.dart';
import '../../presentation/viewmodels/create_flashcard_view_model.dart';
import '../../presentation/viewmodels/study_view_model.dart';
import '../../presentation/viewmodels/analytics_view_model.dart';
import '../../presentation/viewmodels/settings_view_model.dart';
import '../../presentation/viewmodels/main_navigation_view_model.dart';
import '../../presentation/viewmodels/practice_session_view_model.dart';
import '../../presentation/viewmodels/study_session_view_model.dart';
import '../../domain/usecases/study/get_cards_for_study_mode.dart';
import '../../domain/usecases/study/record_study_result.dart';
import '../../domain/usecases/study/get_study_statistics.dart';
import 'injection_container.config.dart';

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async => getIt.init();

Future<void> initializeDependencies() async {
  // Initialize Hive
  await Hive.initFlutter();
  
  // Configure dependency injection (handles auto-registered services)
  await configureDependencies();
  
  // Register core services
  if (!getIt.isRegistered<NavigationService>()) {
    getIt.registerLazySingleton<NavigationService>(() => NavigationService());
  }
  
  // Register repositories that aren't auto-registered (User and Theme)
  if (!getIt.isRegistered<UserRepository>()) {
    getIt.registerLazySingleton<UserRepository>(() => UserRepositoryImpl());
  }
  
  if (!getIt.isRegistered<ThemeRepository>()) {
    getIt.registerLazySingleton<ThemeRepository>(() => ThemeRepositoryImpl());
  }
  
  // Register use cases that aren't auto-registered (User and Theme)
  if (!getIt.isRegistered<GetCurrentUser>()) {
    getIt.registerLazySingleton(() => GetCurrentUser(getIt<UserRepository>()));
  }
  
  if (!getIt.isRegistered<LoginUser>()) {
    getIt.registerLazySingleton(() => LoginUser(getIt<UserRepository>()));
  }
  
  if (!getIt.isRegistered<RegisterUser>()) {
    getIt.registerLazySingleton(() => RegisterUser(getIt<UserRepository>()));
  }
  
  if (!getIt.isRegistered<LogoutUser>()) {
    getIt.registerLazySingleton(() => LogoutUser(getIt<UserRepository>()));
  }
  
  if (!getIt.isRegistered<GetThemeSettings>()) {
    getIt.registerLazySingleton(() => GetThemeSettings(getIt<ThemeRepository>()));
  }
  
  if (!getIt.isRegistered<SetThemeMode>()) {
    getIt.registerLazySingleton(() => SetThemeMode(getIt<ThemeRepository>()));
  }
  
  // Register ViewModels
  if (!getIt.isRegistered<UserViewModel>()) {
    getIt.registerFactory(() => UserViewModel(
      getCurrentUser: getIt<GetCurrentUser>(),
      loginUser: getIt<LoginUser>(),
      registerUser: getIt<RegisterUser>(),
      logoutUser: getIt<LogoutUser>(),
      navigationService: getIt<NavigationService>(),
    ));
  }
  
  if (!getIt.isRegistered<ThemeViewModel>()) {
    getIt.registerFactory(() => ThemeViewModel(
      getThemeSettings: getIt<GetThemeSettings>(),
      setThemeMode: getIt<SetThemeMode>(),
      navigationService: getIt<NavigationService>(),
    ));
  }
  
  if (!getIt.isRegistered<DeckViewModel>()) {
    getIt.registerFactory(() => DeckViewModel(
      getDecks: getIt(),
      createDeck: getIt(),
      updateDeck: getIt(),
      deleteDeck: getIt(),
      navigationService: getIt(),
    ));
  }
  
  // Register new ViewModels for MVVM compliance
  if (!getIt.isRegistered<DeckDetailViewModel>()) {
    getIt.registerFactory(() => DeckDetailViewModel(
      getDeckById: getIt(),
      updateDeck: getIt(),
      deleteDeck: getIt(),
      createFlashcard: getIt(),
      updateFlashcard: getIt(),
      deleteFlashcard: getIt(),
      navigationService: getIt(),
    ));
  }
  
  if (!getIt.isRegistered<MainNavigationViewModel>()) {
    getIt.registerFactory(() => MainNavigationViewModel(
      navigationService: getIt(),
    ));
  }
  
  if (!getIt.isRegistered<HomeViewModel>()) {
    getIt.registerFactory(() => HomeViewModel(
      getDecks: getIt(),
      createDeck: getIt(),
      deleteDeck: getIt(),
      navigationService: getIt(),
    ));
  }

  // Register additional MVVM ViewModels
  if (!getIt.isRegistered<CreateDeckViewModel>()) {
    getIt.registerFactory(() => CreateDeckViewModel(
      createDeck: getIt(),
      navigationService: getIt(),
    ));
  }

  if (!getIt.isRegistered<CreateFlashcardViewModel>()) {
    getIt.registerFactory(() => CreateFlashcardViewModel(
      addFlashcardToDeck: getIt(),
      navigationService: getIt(),
    ));
  }

  if (!getIt.isRegistered<StudyViewModel>()) {
    getIt.registerFactory(() => StudyViewModel(
      updateDeckLastStudied: getIt(),
      navigationService: getIt(),
    ));
  }

  if (!getIt.isRegistered<AnalyticsViewModel>()) {
    getIt.registerFactory(() => AnalyticsViewModel(
      getDecks: getIt(),
    ));
  }

  if (!getIt.isRegistered<SettingsViewModel>()) {
    getIt.registerFactory(() => SettingsViewModel(
      getSettings: getIt(),
      updateSettings: getIt(),
      getThemeMode: getIt(),
      setThemeMode: getIt(),
      navigationService: getIt(),
    ));
  }

  if (!getIt.isRegistered<PracticeSessionViewModel>()) {
    getIt.registerFactory(() => PracticeSessionViewModel(
      navigationService: getIt(),
    ));
  }

  // Register new study use cases
  if (!getIt.isRegistered<GetCardsForStudyMode>()) {
    getIt.registerLazySingleton(() => GetCardsForStudyMode());
  }

  if (!getIt.isRegistered<RecordStudyResult>()) {
    getIt.registerLazySingleton(() => RecordStudyResult(getIt()));
  }

  if (!getIt.isRegistered<GetStudyStatistics>()) {
    getIt.registerLazySingleton(() => GetStudyStatistics());
  }

  // Register new study session ViewModel
  if (!getIt.isRegistered<StudySessionViewModel>()) {
    getIt.registerFactory(() => StudySessionViewModel(
      getCardsForStudyMode: getIt<GetCardsForStudyMode>(),
      recordStudyResult: getIt<RecordStudyResult>(),
      completeStudySession: getIt(),
      startStudySession: getIt(),
      navigationService: getIt<NavigationService>(),
    ));
  }
} 