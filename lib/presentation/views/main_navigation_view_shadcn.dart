import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/di/injection_container.dart';
import '../widgets/shadcn/index.dart';
import '../widgets/shadcn/shadcn_navigation_widget.dart';
import 'home/home_view_shadcn.dart';
import 'study/study_tab_shadcn.dart';
import 'analytics/analytics_tab_shadcn.dart';
import 'settings/settings_tab_shadcn.dart';
import '../viewmodels/home_view_model.dart';
import '../viewmodels/deck_view_model.dart';
import '../viewmodels/main_navigation_view_model.dart';
import '../viewmodels/theme_view_model.dart';

/// Migrated main navigation view using shadcn_flutter components
/// Maintains the same functionality and MVVM architecture while using shadcn design system
class MainNavigationViewShadcn extends StatefulWidget {
  const MainNavigationViewShadcn({super.key});

  @override
  State<MainNavigationViewShadcn> createState() => _MainNavigationViewShadcnState();
}

class _MainNavigationViewShadcnState extends State<MainNavigationViewShadcn> {
  
  // Navigation items configuration
  late final List<ShadcnNavigationItem> _navigationItems;
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _initializeNavigation();
  }

  void _initializeNavigation() {
    // Define navigation items with shadcn design
    _navigationItems = [
      ShadcnNavigationItem(
        icon: Icons.home_outlined,
        selectedIcon: Icons.home_rounded,
        label: 'Home',
        activeColor: const Color(0xFF9333EA),
        gradientColors: [
          const Color(0xFF9333EA),
          const Color(0xFF7C3AED),
          const Color(0xFF6366F1),
        ],
      ),
      ShadcnNavigationItem(
        icon: Icons.school_outlined,
        selectedIcon: Icons.school_rounded,
        label: 'Study',
        activeColor: const Color(0xFF06B6D4),
        gradientColors: [
          const Color(0xFF06B6D4), 
          const Color(0xFF3B82F6),
          const Color(0xFF1E40AF),
        ],
      ),
      ShadcnNavigationItem(
        icon: Icons.analytics_outlined,
        selectedIcon: Icons.analytics_rounded,
        label: 'Analytics',
        activeColor: const Color(0xFFFFB800),
        gradientColors: [
          const Color(0xFFFFB800), 
          const Color(0xFFFF8A00),
          const Color(0xFFEF4444),
        ],
      ),
      ShadcnNavigationItem(
        icon: Icons.settings_outlined,
        selectedIcon: Icons.settings_rounded,
        label: 'Settings',
        activeColor: const Color(0xFFEC4899),
        gradientColors: [
          const Color(0xFFEC4899), 
          const Color(0xFFDC2626),
          const Color(0xFF7C2D12),
        ],
      ),
    ];

    // Define pages with proper provider setup
    _pages = [
      // Home page with providers
      MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (context) => getIt<HomeViewModel>()..loadDecks(),
          ),
          ChangeNotifierProvider(
            create: (context) => getIt<ThemeViewModel>(),
          ),
        ],
        child: const HomeViewShadcn(),
      ),
      
      // Study page
      const StudyTabShadcn(),

      // Analytics page
      const AnalyticsTabShadcn(),
      
      // Settings page
      const SettingsTabShadcn(),
    ];
  }

  Future<void> _handleCreateTap(BuildContext context, MainNavigationViewModel viewModel) async {
    final result = await viewModel.navigateToCreateDeck();
    // If a deck was created, reload the decks
    if (result == true && context.mounted) {
      final deckViewModel = context.read<DeckViewModel>();
      deckViewModel.loadDecks();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => getIt<MainNavigationViewModel>(),
      child: Consumer<MainNavigationViewModel>(
        builder: (context, viewModel, child) {
          return ShadcnNavigationScaffold(
            currentIndex: viewModel.currentIndex,
            navigationItems: _navigationItems,
            pages: _pages,
            onNavigationTap: viewModel.onTabTapped,
            onCreateTap: () => _handleCreateTap(context, viewModel),
          );
        },
      ),
    );
  }
}

/// Alternative implementation using individual shadcn components
/// This provides more granular control over the navigation
class MainNavigationViewShadcnDetailed extends StatefulWidget {
  const MainNavigationViewShadcnDetailed({super.key});

  @override
  State<MainNavigationViewShadcnDetailed> createState() => _MainNavigationViewShadcnDetailedState();
}

class _MainNavigationViewShadcnDetailedState extends State<MainNavigationViewShadcnDetailed> {
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => getIt<MainNavigationViewModel>(),
      child: Consumer<MainNavigationViewModel>(
        builder: (context, viewModel, child) {
          final theme = Theme.of(context);

          return Scaffold(
            extendBody: true,
            backgroundColor: theme.colorScheme.surface,
            body: AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
              decoration: BoxDecoration(
                gradient: theme.brightness == Brightness.dark
                    ? LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          theme.colorScheme.surface,
                          theme.colorScheme.surfaceContainerHighest,
                        ],
                      )
                    : LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          theme.colorScheme.surface,
                          theme.colorScheme.surfaceContainerHighest,
                        ],
                      ),
              ),
              child: IndexedStack(
                index: viewModel.currentIndex,
                children: [
                  // Home page with providers
                  MultiProvider(
                    providers: [
                      ChangeNotifierProvider(
                        create: (context) => getIt<HomeViewModel>()..loadDecks(),
                      ),
                      ChangeNotifierProvider(
                        create: (context) => getIt<ThemeViewModel>(),
                      ),
                    ],
                    child: const HomeViewShadcn(),
                  ),
                  
                  // Other pages
                  const StudyTabShadcn(),
                  const AnalyticsTabShadcn(),
                  const SettingsTabShadcn(),
                ],
              ),
            ),
            bottomNavigationBar: ShadcnNavigationWidget(
              currentIndex: viewModel.currentIndex,
              items: [
                ShadcnNavigationItem(
                  icon: Icons.home_outlined,
                  selectedIcon: Icons.home_rounded,
                  label: 'Home',
                  activeColor: const Color(0xFF9333EA),
                ),
                ShadcnNavigationItem(
                  icon: Icons.school_outlined,
                  selectedIcon: Icons.school_rounded,
                  label: 'Study',
                  activeColor: const Color(0xFF06B6D4),
                ),
                ShadcnNavigationItem(
                  icon: Icons.analytics_outlined,
                  selectedIcon: Icons.analytics_rounded,
                  label: 'Analytics',
                  activeColor: const Color(0xFFFFB800),
                ),
                ShadcnNavigationItem(
                  icon: Icons.settings_outlined,
                  selectedIcon: Icons.settings_rounded,
                  label: 'Settings',
                  activeColor: const Color(0xFFEC4899),
                ),
              ],
              onTap: viewModel.onTabTapped,
              onCreateTap: () async {
                final result = await viewModel.navigateToCreateDeck();
                if (result == true && context.mounted) {
                  final deckViewModel = context.read<DeckViewModel>();
                  deckViewModel.loadDecks();
                }
              },
            ),
          );
        },
      ),
    );
  }
}
