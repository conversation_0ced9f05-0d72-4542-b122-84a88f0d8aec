import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';

import '../../../domain/entities/deck.dart';
import '../../viewmodels/home_view_model.dart';
import '../../widgets/shadcn/index.dart';
import '../../../core/theme/app_theme.dart';

/// Migrated Home View using shadcn_flutter components
/// Maintains the same functionality and MVVM architecture while using shadcn design system
class HomeViewShadcn extends StatefulWidget {
  const HomeViewShadcn({super.key});

  @override
  State<HomeViewShadcn> createState() => _HomeViewShadcnState();
}

class _HomeViewShadcnState extends State<HomeViewShadcn> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeViewModel>().loadDecks();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildHeader(theme, size),
            _buildSearchBar(theme),
            _buildDecksGrid(theme),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(theme),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  BoxDecoration _buildBackgroundDecoration(ThemeData theme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: theme.brightness == Brightness.dark
            ? [
                const Color(0xFF0F172A),
                const Color(0xFF1E293B),
                const Color(0xFF334155),
              ]
            : [
                const Color(0xFFFAFAFA),
                const Color(0xFFF1F5F9),
                const Color(0xFFE2E8F0),
              ],
        stops: const [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, Size size) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 60), // Extra padding since no app bar
            _buildWelcomeSection(theme),
            const SizedBox(height: 24),
            _buildStatsCards(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome back!',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Ready to expand your knowledge?',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCards(ThemeData theme) {
    return Consumer<HomeViewModel>(
      builder: (context, homeViewModel, _) {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'Decks',
                value: homeViewModel.totalDecks.toString(),
                icon: PhosphorIcons.stack(),
                gradient: AppTheme.primaryGradient,
                theme: theme,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                title: 'Cards',
                value: homeViewModel.totalCards.toString(),
                icon: PhosphorIcons.cards(),
                gradient: AppTheme.secondaryGradient,
                theme: theme,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required List<Color> gradient,
    required ThemeData theme,
  }) {
    return ShadcnCardWidget(
      height: 100,
      gradient: gradient,
      animationDelay: 0,
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Consumer<HomeViewModel>(
          builder: (context, homeViewModel, _) {
            return ShadcnInputWidget(
              controller: _searchController,
              label: '',
              hint: 'Search decks...',
              prefixIcon: PhosphorIcons.magnifyingGlass(),
              suffixIcon: homeViewModel.isSearching ? PhosphorIcons.x() : null,
              onChanged: homeViewModel.updateSearchQuery,
            );
          },
        ),
      ),
    );
  }

  Widget _buildDecksGrid(ThemeData theme) {
    return SliverPadding(
      padding: const EdgeInsets.all(24),
      sliver: Consumer<HomeViewModel>(
        builder: (context, homeViewModel, _) {
          if (homeViewModel.isLoading) {
            return _buildLoadingGrid();
          }

          if (homeViewModel.error != null) {
            return _buildErrorState(theme, homeViewModel.error!);
          }

          final filteredDecks = homeViewModel.filteredDecks;

          if (filteredDecks.isEmpty) {
            return _buildEmptyState(theme);
          }

          return SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return _buildDeckCard(filteredDecks[index], theme);
              },
              childCount: filteredDecks.length,
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return SliverGrid(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return ShadcnSkeletonWidget(
            borderRadius: BorderRadius.circular(20),
          );
        },
        childCount: 6,
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, String error) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              PhosphorIcons.warningCircle(),
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Decks',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ShadcnButtonWidget(
              text: 'Retry',
              onPressed: () => context.read<HomeViewModel>().loadDecks(),
              variant: ShadcnButtonVariant.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient.map((c) => c.withValues(alpha: 0.3)).toList(),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                PhosphorIcons.stack(),
                size: 60,
                color: AppTheme.primaryGradient.first,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Decks Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first deck to start learning!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadcnButtonWidget(
              text: 'Create Deck',
              icon: PhosphorIcons.plus(),
              onPressed: _navigateToCreateDeck,
              variant: ShadcnButtonVariant.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeckCard(Deck deck, ThemeData theme) {
    return ShadcnCardWidget(
      onTap: () => _navigateToDeckDetail(deck),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: AppTheme.primaryGradient),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(
              PhosphorIcons.stack(),
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            deck.name,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          if (deck.description.isNotEmpty) ...[
            Text(
              deck.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
          ],
          const Spacer(),
          Row(
            children: [
              Icon(
                PhosphorIcons.cards(),
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Text(
                '${deck.flashcards.length} cards',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(ThemeData theme) {
    return ShadcnFloatingActionButton(
      onPressed: _navigateToCreateDeck,
      icon: PhosphorIcons.plus(),
      gradient: AppTheme.primaryGradient,
      tooltip: 'Create Deck',
    );
  }

  void _navigateToCreateDeck() {
    context.read<HomeViewModel>().navigateToCreateDeck();
  }

  void _navigateToDeckDetail(Deck deck) {
    context.read<HomeViewModel>().navigateToDeckDetail(deck);
  }
}
