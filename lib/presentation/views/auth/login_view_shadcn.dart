import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../viewmodels/user_view_model.dart';
import '../../widgets/shadcn/index.dart';

/// Migrated login view using shadcn_flutter components
/// Maintains the same functionality and MVVM architecture while using shadcn design system
class LoginViewShadcn extends StatefulWidget {
  const LoginViewShadcn({super.key});

  @override
  State<LoginViewShadcn> createState() => _LoginViewShadcnState();
}

class _LoginViewShadcnState extends State<LoginViewShadcn> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLogin = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final userViewModel = context.read<UserViewModel>();
    
    if (_isLogin) {
      await userViewModel.login(
        _emailController.text.trim(),
        _passwordController.text,
      );
    } else {
      await userViewModel.register(
        _nameController.text.trim(),
        _emailController.text.trim(),
        _passwordController.text,
      );
    }
  }

  String? _validateEmail(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter your email';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter your password';
    }
    if (!_isLogin && value!.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateName(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter your name';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final userViewModel = context.watch<UserViewModel>();

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo and Title
                _buildAppHeader(theme),
                const SizedBox(height: 48),

                // Login Form using shadcn components
                _buildLoginForm(userViewModel),
                
                const SizedBox(height: 24),

                // Toggle between login and register
                _buildToggleSection(theme),
                
                const SizedBox(height: 32),

                // Demo account notice using shadcn Alert
                _buildDemoNotice(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppHeader(ThemeData theme) {
    return Column(
      children: [
        // App Logo using shadcn design principles
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            Icons.school,
            size: 40,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(height: 32),
        
        Text(
          'FlashCards Pro',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        
        Text(
          _isLogin ? 'Welcome back' : 'Create your account',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm(UserViewModel userViewModel) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Name field for registration
          if (!_isLogin) ...[
            ShadcnInputWidget(
              controller: _nameController,
              label: 'Full Name',
              hint: 'Enter your full name',
              prefixIcon: Icons.person_outline,
              validator: _validateName,
              required: true,
            ),
            const SizedBox(height: 16),
          ],
          
          // Email field
          ShadcnInputWidget(
            controller: _emailController,
            label: 'Email',
            hint: 'Enter your email address',
            prefixIcon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: _validateEmail,
            required: true,
          ),
          const SizedBox(height: 16),
          
          // Password field
          ShadcnInputWidget(
            controller: _passwordController,
            label: 'Password',
            hint: 'Enter your password',
            prefixIcon: Icons.lock_outline,
            isPassword: true,
            validator: _validatePassword,
            required: true,
          ),
          const SizedBox(height: 32),

          // Submit Button using shadcn Button
          ShadcnButtonWidget(
            text: _isLogin ? 'Sign In' : 'Create Account',
            onPressed: userViewModel.isLoading ? null : _handleSubmit,
            variant: ShadcnButtonVariant.primary,
            isLoading: userViewModel.isLoading,
            isExpanded: true,
            height: 56,
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSection(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _isLogin ? "Don't have an account? " : "Already have an account? ",
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        ShadcnButtonWidget(
          text: _isLogin ? 'Sign Up' : 'Sign In',
          onPressed: () {
            setState(() {
              _isLogin = !_isLogin;
            });
          },
          variant: ShadcnButtonVariant.ghost,
        ),
      ],
    );
  }

  Widget _buildDemoNotice() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            'Demo Version',
            style: theme.textTheme.labelLarge?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Use any email and password to continue',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
