import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../../../domain/entities/deck.dart';
import '../../../domain/entities/study_mode.dart';
import '../../viewmodels/deck_view_model.dart';
import '../../viewmodels/study_session_view_model.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/di/injection_container.dart';
import '../../widgets/shadcn/index.dart';
import 'study_session_view_shadcn.dart';

/// Migrated Study Tab using shadcn_flutter components
/// Maintains the same functionality and MVVM architecture while using shadcn design system
class StudyTabShadcn extends StatefulWidget {
  const StudyTabShadcn({super.key});

  @override
  State<StudyTabShadcn> createState() => _StudyTabShadcnState();
}

class _StudyTabShadcnState extends State<StudyTabShadcn> {
  StudyMode? _selectedMode;

  void _selectStudyMode(StudyMode mode) {
    setState(() {
      _selectedMode = mode;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: Consumer<DeckViewModel>(
          builder: (context, deckViewModel, child) {
            if (deckViewModel.decks.isEmpty) {
              return _buildEmptyState(context);
            }

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                const SliverToBoxAdapter(child: SizedBox(height: 60)),

                // Header
                SliverToBoxAdapter(
                  child: _buildHeader(theme),
                ),

                // Study Mode Selection
                SliverToBoxAdapter(
                  child: _buildStudyModeSelection(context),
                ),

                // Available Decks Header
                SliverToBoxAdapter(
                  child: _buildDecksHeader(theme, deckViewModel),
                ),

                // Deck List
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(24, 0, 24, 120),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final deck = deckViewModel.decks[index];
                        return _buildDeckCard(context, deck, index);
                      },
                      childCount: deckViewModel.decks.length,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: AppTheme.primaryGradient),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  PhosphorIcons.graduationCap(),
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Study Session',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      'Choose your study mode and deck',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStudyModeSelection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  PhosphorIcons.target(),
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Choose Study Mode',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModeGrid(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildModeGrid(ThemeData theme) {
    final modes = [
      _StudyModeInfo(
        mode: StudyMode.review,
        title: 'Review',
        description: 'Quick review of all cards',
        icon: PhosphorIcons.clockCounterClockwise(),
        gradient: AppTheme.primaryGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.learn,
        title: 'Learn',
        description: 'Focus on new cards',
        icon: PhosphorIcons.graduationCap(),
        gradient: AppTheme.secondaryGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.test,
        title: 'Test',
        description: 'Challenge yourself',
        icon: PhosphorIcons.exam(),
        gradient: AppTheme.accentGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.weakCards,
        title: 'Weak Cards',
        description: 'Practice difficult cards',
        icon: PhosphorIcons.trendUp(),
        gradient: [const Color(0xFFEC4899), const Color(0xFFDC2626)],
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: modes.length,
      itemBuilder: (context, index) {
        final mode = modes[index];
        final isSelected = _selectedMode == mode.mode;
        
        return ShadcnCardWidget(
          onTap: () => _selectStudyMode(mode.mode),
          gradient: isSelected
              ? mode.gradient.map((c) => c.withValues(alpha: 0.15)).toList()
              : mode.gradient.map((c) => c.withValues(alpha: 0.05)).toList(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: mode.gradient[0].withValues(alpha: isSelected ? 0.2 : 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  mode.icon,
                  color: mode.gradient[0],
                  size: 16,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                mode.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Expanded(
                child: Text(
                  mode.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w400,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (isSelected)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    children: [
                      Icon(
                        PhosphorIcons.checkCircle(PhosphorIconsStyle.fill),
                        color: mode.gradient[0],
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Selected',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: mode.gradient[0],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDecksHeader(ThemeData theme, DeckViewModel deckViewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                PhosphorIcons.stack(),
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Available Decks',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Badge(
            child: Text(
              '${deckViewModel.decks.length} decks',
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeckCard(BuildContext context, Deck deck, int index) {
    final theme = Theme.of(context);
    final gradients = [
      AppTheme.primaryGradient,
      AppTheme.secondaryGradient,
      AppTheme.accentGradient,
      [const Color(0xFFEC4899), const Color(0xFFDC2626)],
    ];
    final gradient = gradients[index % gradients.length];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ShadcnCardWidget(
        onTap: () => _startStudySession(context, deck),
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: gradient),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                PhosphorIcons.stack(),
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    deck.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        PhosphorIcons.cards(),
                        size: 14,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${deck.flashcards.length} cards',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      if (deck.lastStudied != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _getLastStudiedText(deck.lastStudied!),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            if (_selectedMode != null)
              ShadcnButtonWidget(
                text: 'Start',
                icon: PhosphorIcons.play(),
                variant: ShadcnButtonVariant.primary,
                onPressed: () => _startStudySession(context, deck),
              )
            else
              Icon(
                PhosphorIcons.arrowRight(),
                color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  String _getLastStudiedText(DateTime lastStudied) {
    final now = DateTime.now();
    final difference = now.difference(lastStudied);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _startStudySession(BuildContext context, Deck deck) {
    if (_selectedMode == null) {
      _showStudyModeDialog(context, deck);
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => getIt<StudySessionViewModel>(),
            child: StudySessionViewShadcn(
              deck: deck,
              studyMode: _selectedMode!,
            ),
          ),
        ),
      );
    }
  }

  void _showStudyModeDialog(BuildContext context, Deck deck) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: ShadcnCardWidget(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    PhosphorIcons.target(),
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Choose Study Mode',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...StudyMode.values.map((mode) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ShadcnButtonWidget(
                    text: mode.displayName,
                    icon: _getStudyModeIcon(mode),
                    variant: ShadcnButtonVariant.ghost,
                    isExpanded: true,
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ChangeNotifierProvider(
                            create: (_) => getIt<StudySessionViewModel>(),
                            child: StudySessionViewShadcn(
                              deck: deck,
                              studyMode: mode,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),
              const SizedBox(height: 16),
              ShadcnButtonWidget(
                text: 'Cancel',
                variant: ShadcnButtonVariant.outline,
                isExpanded: true,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStudyModeIcon(StudyMode mode) {
    switch (mode) {
      case StudyMode.review:
        return PhosphorIcons.clockCounterClockwise();
      case StudyMode.practice:
        return PhosphorIcons.dumbbell();
      case StudyMode.test:
        return PhosphorIcons.exam();
      case StudyMode.learn:
        return PhosphorIcons.graduationCap();
      case StudyMode.weakCards:
        return PhosphorIcons.trendUp();
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(48),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient.map((c) => c.withValues(alpha: 0.3)).toList(),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                PhosphorIcons.graduationCap(),
                size: 60,
                color: AppTheme.primaryGradient.first,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Decks to Study',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first deck to start studying!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadcnButtonWidget(
              text: 'Create Deck',
              icon: PhosphorIcons.plus(),
              variant: ShadcnButtonVariant.primary,
              onPressed: () {
                // Navigate to create deck
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _StudyModeInfo {
  final StudyMode mode;
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradient;

  _StudyModeInfo({
    required this.mode,
    required this.title,
    required this.description,
    required this.icon,
    required this.gradient,
  });
}
