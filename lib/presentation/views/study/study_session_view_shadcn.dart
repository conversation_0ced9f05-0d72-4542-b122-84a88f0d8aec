import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../../../domain/entities/deck.dart';
import '../../../domain/entities/study_mode.dart';
import '../../viewmodels/study_session_view_model.dart';
import '../../widgets/shadcn/index.dart';
import '../../../core/theme/app_theme.dart';

/// Migrated Study Session View using shadcn_flutter components
/// Maintains the same functionality while using shadcn design system
class StudySessionViewShadcn extends StatefulWidget {
  final Deck deck;
  final StudyMode studyMode;

  const StudySessionViewShadcn({
    super.key,
    required this.deck,
    required this.studyMode,
  });

  @override
  State<StudySessionViewShadcn> createState() => _StudySessionViewShadcnState();
}

class _StudySessionViewShadcnState extends State<StudySessionViewShadcn> {
  @override
  void initState() {
    super.initState();
    _startSession();
  }

  void _startSession() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<StudySessionViewModel>();
      viewModel.startSession(widget.deck, widget.studyMode);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<StudySessionViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoading) {
          return _buildLoadingScreen(theme);
        }

        if (viewModel.hasError) {
          return _buildErrorScreen(theme, viewModel.error!);
        }

        if (viewModel.isSessionComplete) {
          return _buildCompletionScreen(theme, viewModel);
        }

        return _buildStudyScreen(theme, viewModel);
      },
    );
  }

  Widget _buildLoadingScreen(ThemeData theme) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: Center(
          child: ShadcnCardWidget(
            padding: const EdgeInsets.all(40),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: AppTheme.primaryGradient),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Preparing Study Session',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Setting up your flashcards...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorScreen(ThemeData theme, String error) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Row(
                  children: [
                    Button(
                      onPressed: () => Navigator.of(context).pop(),
                      variant: ButtonVariant.ghost,
                      size: ButtonSize.sm,
                      child: Icon(PhosphorIcons.arrowLeft()),
                    ),
                  ],
                ),
                Expanded(
                  child: Center(
                    child: ShadcnCardWidget(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(40),
                            ),
                            child: Icon(
                              PhosphorIcons.warning(),
                              size: 40,
                              color: theme.colorScheme.error,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Study Session Error',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            error,
                            textAlign: TextAlign.center,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 32),
                          ShadcnButtonWidget(
                            text: 'Go Back',
                            variant: ShadcnButtonVariant.primary,
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStudyScreen(ThemeData theme, StudySessionViewModel viewModel) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(theme, viewModel),
              _buildProgressSection(theme, viewModel),
              Expanded(
                child: _buildCardSection(theme, viewModel),
              ),
              _buildControlsSection(theme, viewModel),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, StudySessionViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Button(
            onPressed: () => _showExitDialog(context, viewModel),
            variant: ButtonVariant.ghost,
            size: ButtonSize.sm,
            child: Icon(PhosphorIcons.x()),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  widget.studyMode.displayName,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  widget.deck.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Button(
            onPressed: () => _showResetDialog(context, viewModel),
            variant: ButtonVariant.ghost,
            size: ButtonSize.sm,
            child: Icon(PhosphorIcons.clockCounterClockwise()),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme, StudySessionViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatCard(
                  theme,
                  'Progress',
                  viewModel.progressText,
                  PhosphorIcons.chartLine(),
                  AppTheme.primaryGradient.first,
                ),
                _buildStatCard(
                  theme,
                  'Accuracy',
                  viewModel.accuracyText,
                  PhosphorIcons.target(),
                  AppTheme.successGradient.first,
                ),
                _buildStatCard(
                  theme,
                  'Time',
                  viewModel.sessionDurationText,
                  PhosphorIcons.timer(),
                  AppTheme.secondaryGradient.first,
                ),
              ],
            ),
            const SizedBox(height: 20),
            ShadcnProgressWidget(
              value: viewModel.progress,
              height: 8,
              color: theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(ThemeData theme, String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildCardSection(ThemeData theme, StudySessionViewModel viewModel) {
    if (viewModel.currentCard == null) {
      return Center(
        child: ShadcnCardWidget(
          padding: const EdgeInsets.all(40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                PhosphorIcons.cards(),
                size: 60,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              ),
              const SizedBox(height: 16),
              Text(
                'No card available',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(24),
      child: ShadcnFlashCardWidget(
        flashcard: viewModel.currentCard!,
        showAnswer: viewModel.showAnswer,
        isFlipping: viewModel.isFlipping,
        onTap: () => viewModel.toggleAnswer(),
        gradient: AppTheme.primaryGradient,
      ),
    );
  }

  Widget _buildControlsSection(ThemeData theme, StudySessionViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          if (!viewModel.showAnswer) ...[
            ShadcnButtonWidget(
              text: 'Show Answer',
              icon: PhosphorIcons.eye(),
              variant: ShadcnButtonVariant.primary,
              isExpanded: true,
              height: 56,
              isLoading: viewModel.isFlipping,
              onPressed: viewModel.isFlipping ? null : () => viewModel.toggleAnswer(),
            ),
          ] else ...[
            Row(
              children: [
                Expanded(
                  child: ShadcnButtonWidget(
                    text: 'Incorrect',
                    icon: PhosphorIcons.x(),
                    variant: ShadcnButtonVariant.destructive,
                    height: 56,
                    onPressed: viewModel.isTransitioning
                        ? null
                        : () => _answerCard(viewModel, false),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ShadcnButtonWidget(
                    text: 'Correct',
                    icon: PhosphorIcons.check(),
                    variant: ShadcnButtonVariant.success,
                    height: 56,
                    onPressed: viewModel.isTransitioning
                        ? null
                        : () => _answerCard(viewModel, true),
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Button(
                onPressed: viewModel.hasPreviousCard ? () => viewModel.previousCard() : null,
                variant: ButtonVariant.ghost,
                size: ButtonSize.sm,
                child: Icon(PhosphorIcons.caretLeft()),
              ),
              Text(
                '${viewModel.currentCardIndex + 1} of ${viewModel.totalCards}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Button(
                onPressed: viewModel.hasNextCard ? () => viewModel.nextCard() : null,
                variant: ButtonVariant.ghost,
                size: ButtonSize.sm,
                child: Icon(PhosphorIcons.caretRight()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionScreen(ThemeData theme, StudySessionViewModel viewModel) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Expanded(
                  child: Center(
                    child: ShadcnCardWidget(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(colors: AppTheme.successGradient),
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: Icon(
                              PhosphorIcons.trophy(),
                              size: 50,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 32),
                          Text(
                            'Session Complete!',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Great job! You\'ve completed your study session.',
                            textAlign: TextAlign.center,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 32),
                          _buildSessionStats(theme, viewModel),
                        ],
                      ),
                    ),
                  ),
                ),
                Column(
                  children: [
                    ShadcnButtonWidget(
                      text: 'Study Again',
                      icon: PhosphorIcons.repeat(),
                      variant: ShadcnButtonVariant.primary,
                      isExpanded: true,
                      height: 56,
                      onPressed: () => viewModel.restartSession(),
                    ),
                    const SizedBox(height: 16),
                    ShadcnButtonWidget(
                      text: 'Back to Decks',
                      variant: ShadcnButtonVariant.outline,
                      isExpanded: true,
                      height: 56,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSessionStats(ThemeData theme, StudySessionViewModel viewModel) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildCompletionStat(
              theme,
              'Cards Studied',
              '${viewModel.totalCards}',
              PhosphorIcons.cards(),
            ),
            _buildCompletionStat(
              theme,
              'Accuracy',
              viewModel.accuracyText,
              PhosphorIcons.target(),
            ),
            _buildCompletionStat(
              theme,
              'Time Spent',
              viewModel.sessionDurationText,
              PhosphorIcons.timer(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompletionStat(ThemeData theme, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  void _answerCard(StudySessionViewModel viewModel, bool isCorrect) {
    HapticFeedback.lightImpact();
    viewModel.answerCard(isCorrect);
  }

  void _showExitDialog(BuildContext context, StudySessionViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: ShadcnCardWidget(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                PhosphorIcons.warning(),
                size: 48,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Exit Study Session?',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your progress will be saved, but you\'ll lose your current session state.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ShadcnButtonWidget(
                      text: 'Cancel',
                      variant: ShadcnButtonVariant.outline,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadcnButtonWidget(
                      text: 'Exit',
                      variant: ShadcnButtonVariant.destructive,
                      onPressed: () {
                        Navigator.of(context).pop(); // Close dialog
                        Navigator.of(context).pop(); // Exit session
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, StudySessionViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: ShadcnCardWidget(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                PhosphorIcons.clockCounterClockwise(),
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Reset Session?',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This will restart your study session from the beginning.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ShadcnButtonWidget(
                      text: 'Cancel',
                      variant: ShadcnButtonVariant.outline,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadcnButtonWidget(
                      text: 'Reset',
                      variant: ShadcnButtonVariant.primary,
                      onPressed: () {
                        Navigator.of(context).pop();
                        viewModel.restartSession();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
