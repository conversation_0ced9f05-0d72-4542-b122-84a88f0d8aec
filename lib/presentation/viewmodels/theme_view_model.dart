import 'package:flutter/material.dart';
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/theme_settings.dart';
import '../../domain/usecases/theme/get_theme_settings.dart';
import '../../domain/usecases/theme/set_theme_mode.dart';

class ThemeViewModel extends BaseViewModel {
  final GetThemeSettings _getThemeSettings;
  final SetThemeMode _setThemeMode;
  final NavigationService _navigationService;

  ThemeSettings _themeSettings = const ThemeSettings();
  ThemeSettings get themeSettings => _themeSettings;
  ThemeMode get themeMode => _themeSettings.themeMode;

  bool get isDarkMode => _themeSettings.isDarkMode;
  bool get isLightMode => _themeSettings.isLightMode;
  bool get isSystemMode => _themeSettings.isSystemMode;

  ThemeViewModel({
    required GetThemeSettings getThemeSettings,
    required SetThemeMode setThemeMode,
    required NavigationService navigationService,
  })  : _getThemeSettings = getThemeSettings,
        _setThemeMode = setThemeMode,
        _navigationService = navigationService;

  Future<void> loadTheme() async {
    setLoading(true);
    clearError();

    final result = await _getThemeSettings(const NoParams());
    result.fold(
      (failure) => setError('Failed to load theme: ${failure.message}'),
      (settings) {
        _themeSettings = settings;
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeSettings.themeMode == mode) return;

    setLoading(true);
    clearError();

    final result = await _setThemeMode(SetThemeModeParams(themeMode: mode));
    result.fold(
      (failure) => setError('Failed to set theme: ${failure.message}'),
      (_) {
        _themeSettings = _themeSettings.copyWith(themeMode: mode);
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> setThemeModeAndClose(ThemeMode mode) async {
    await setThemeMode(mode);
    _navigationService.goBack();
  }

  Future<void> toggleTheme() async {
    final newSettings = _themeSettings.toggleTheme();
    await setThemeMode(newSettings.themeMode);
  }

  Future<void> setLightMode() async {
    await setThemeMode(ThemeMode.light);
  }

  Future<void> setDarkMode() async {
    await setThemeMode(ThemeMode.dark);
  }

  Future<void> setSystemMode() async {
    await setThemeMode(ThemeMode.system);
  }
} 