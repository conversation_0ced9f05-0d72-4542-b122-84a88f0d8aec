
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/deck.dart';
import '../../domain/usecases/deck/get_decks.dart';
import '../../domain/usecases/deck/create_deck.dart';
import '../../domain/usecases/deck/delete_deck.dart';

class HomeViewModel extends BaseViewModel {
  final GetDecks _getDecks;
  final CreateDeck _createDeck;
  final DeleteDeck _deleteDeck;
  final NavigationService _navigationService;

  HomeViewModel({
    required GetDecks getDecks,
    required CreateDeck createDeck,
    required DeleteDeck deleteDeck,
    required NavigationService navigationService,
  })  : _getDecks = getDecks,
        _createDeck = createDeck,
        _deleteDeck = deleteDeck,
        _navigationService = navigationService;

  List<Deck> _decks = [];
  List<Deck> get decks => _decks;

  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  bool _isSearching = false;
  bool get isSearching => _isSearching;

  // Computed properties
  List<Deck> get filteredDecks {
    if (_searchQuery.isEmpty) return _decks;
    
    return _decks.where((deck) {
      return deck.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             deck.description.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  int get totalDecks => _decks.length;
  
  int get totalCards => _decks.fold<int>(
    0, 
    (sum, deck) => sum + deck.flashcards.length,
  );

  int get totalReviews => _decks.fold<int>(
    0,
    (sum, deck) => sum + deck.flashcards.fold<int>(
      0,
      (cardSum, card) => cardSum + card.reviewCount,
    ),
  );

  bool get hasDecks => _decks.isNotEmpty;
  bool get isEmpty => _decks.isEmpty;

  // Search functionality
  void updateSearchQuery(String query) {
    _searchQuery = query;
    _isSearching = query.isNotEmpty;
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    _isSearching = false;
    notifyListeners();
  }

  // Deck operations
  Future<void> loadDecks() async {
    setLoading(true);
    clearError();

    try {
      final result = await _getDecks(NoParams());
      result.fold(
        (failure) => setError(failure.message),
        (decks) {
          _decks = decks;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to load decks: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> createDeck(String name, String description) async {
    setLoading(true);
    clearError();

    try {
      final result = await _createDeck(CreateDeckParams(
        name: name,
        description: description,
      ));

      result.fold(
        (failure) => setError(failure.message),
        (deck) {
          _decks.add(deck);
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to create deck: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> deleteDeck(String deckId) async {
    setLoading(true);
    clearError();

    try {
      final result = await _deleteDeck(DeleteDeckParams(deckId: deckId));
      result.fold(
        (failure) => setError(failure.message),
        (_) {
          _decks.removeWhere((deck) => deck.id == deckId);
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to delete deck: $e');
    } finally {
      setLoading(false);
    }
  }

  // Statistics helpers
  Map<String, int> get deckStatistics {
    return {
      'totalDecks': totalDecks,
      'totalCards': totalCards,
      'totalReviews': totalReviews,
    };
  }

  List<Deck> get recentDecks {
    final sortedDecks = List<Deck>.from(_decks);
    sortedDecks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedDecks.take(5).toList();
  }

  List<Deck> get mostStudiedDecks {
    final sortedDecks = List<Deck>.from(_decks);
    sortedDecks.sort((a, b) {
      final aReviews = a.flashcards.fold<int>(0, (sum, card) => sum + card.reviewCount);
      final bReviews = b.flashcards.fold<int>(0, (sum, card) => sum + card.reviewCount);
      return bReviews.compareTo(aReviews);
    });
    return sortedDecks.take(5).toList();
  }

  // Navigation helpers
  bool canStudyDeck(String deckId) {
    final deck = _decks.firstWhere(
      (d) => d.id == deckId,
      orElse: () => throw Exception('Deck not found'),
    );
    return deck.flashcards.isNotEmpty;
  }

  Deck? getDeckById(String deckId) {
    try {
      return _decks.firstWhere((deck) => deck.id == deckId);
    } catch (e) {
      return null;
    }
  }

  // Navigation methods
  Future<void> navigateToCreateDeck() async {
    await _navigationService.navigateToCreateDeck();
  }

  Future<void> navigateToDeckDetail(Deck deck) async {
    await _navigationService.navigateToDeckDetail(deck);
  }

  // Refresh functionality
  Future<void> refreshDecks() async {
    await loadDecks();
  }
} 