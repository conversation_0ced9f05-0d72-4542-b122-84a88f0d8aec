import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../domain/usecases/deck/create_deck.dart';

class CreateDeckViewModel extends BaseViewModel {
  final CreateDeck _createDeck;
  final NavigationService _navigationService;

  CreateDeckViewModel({
    required CreateDeck createDeck,
    required NavigationService navigationService,
  }) : _createDeck = createDeck,
       _navigationService = navigationService;

  String _name = '';
  String _description = '';

  String get name => _name;
  String get description => _description;

  void updateName(String value) {
    _name = value;
    notifyListeners();
  }

  void updateDescription(String value) {
    _description = value;
    notifyListeners();
  }

  bool get canSubmit => _name.trim().isNotEmpty;

  Future<void> createDeck() async {
    if (!canSubmit) return;

    setLoading(true);
    clearError();

    try {
      final result = await _createDeck(CreateDeckParams(
        name: _name.trim(),
        description: _description.trim(),
      ));

      result.fold(
        (failure) {
          setError(failure.message);
          _navigationService.showSnackBar('Failed to create deck: ${failure.message}');
        },
        (deck) {
          // Reset form
          _name = '';
          _description = '';
          notifyListeners();
          _navigationService.showSnackBar('Deck created successfully!');
          _navigationService.goBackWithResult(true);
        },
      );
    } catch (e) {
      setError('Failed to create deck: $e');
      _navigationService.showSnackBar('Failed to create deck: $e');
    } finally {
      setLoading(false);
    }
  }

  void navigateBack() {
    _navigationService.goBack();
  }

  void reset() {
    _name = '';
    _description = '';
    clearError();
    notifyListeners();
  }
} 