
import '../../core/viewmodels/base_view_model.dart';
import '../../domain/usecases/flashcard/add_flashcard_to_deck.dart';
import '../../core/services/navigation_service.dart';

class CreateFlashcardViewModel extends BaseViewModel {
  final AddFlashcardToDeck _addFlashcardToDeck;
  final NavigationService _navigationService;

  CreateFlashcardViewModel({
    required AddFlashcardToDeck addFlashcardToDeck,
    required NavigationService navigationService,
  }) : _addFlashcardToDeck = addFlashcardToDeck,
       _navigationService = navigationService;

  String _front = '';
  String _back = '';
  
  String get front => _front;
  String get back => _back;

  void updateFront(String value) {
    _front = value;
    notifyListeners();
  }

  void updateBack(String value) {
    _back = value;
    notifyListeners();
  }

  bool get canSubmit => _front.trim().isNotEmpty && _back.trim().isNotEmpty;

  Future<void> createFlashcard(String deckId) async {
    if (!canSubmit) return;

    setLoading(true);
    clearError();

    try {
      final result = await _addFlashcardToDeck(AddFlashcardToDeckParams(
        deckId: deckId,
        front: _front.trim(),
        back: _back.trim(),
      ));

      result.fold(
        (failure) {
          setError(failure.message);
          _navigationService.showSnackBar('Failed to create flashcard: ${failure.message}');
        },
        (flashcard) {
          // Reset form
          _front = '';
          _back = '';
          notifyListeners();
          _navigationService.showSnackBar('Flashcard created successfully!');
          _navigationService.goBack();
        },
      );
    } catch (e) {
      setError('Failed to create flashcard: $e');
      _navigationService.showSnackBar('Failed to create flashcard: $e');
    } finally {
      setLoading(false);
    }
  }

  void navigateBack() {
    _navigationService.goBack();
  }

  void reset() {
    _front = '';
    _back = '';
    clearError();
    notifyListeners();
  }
} 