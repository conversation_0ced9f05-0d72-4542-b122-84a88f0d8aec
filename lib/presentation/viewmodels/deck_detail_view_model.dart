
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/study_mode.dart';
import '../../domain/usecases/deck/get_deck_by_id.dart';
import '../../domain/usecases/deck/update_deck.dart';
import '../../domain/usecases/deck/delete_deck.dart';
import '../../domain/usecases/flashcard/create_flashcard.dart';
import '../../domain/usecases/flashcard/update_flashcard.dart';
import '../../domain/usecases/flashcard/delete_flashcard.dart';

class DeckDetailViewModel extends BaseViewModel {
  final GetDeckById _getDeckById;
  final UpdateDeck _updateDeck;
  final DeleteDeck _deleteDeck;
  final CreateFlashcard _createFlashcard;
  final UpdateFlashcard _updateFlashcard;
  final DeleteFlashcard _deleteFlashcard;
  final NavigationService _navigationService;

  DeckDetailViewModel({
    required GetDeckById getDeckById,
    required UpdateDeck updateDeck,
    required DeleteDeck deleteDeck,
    required CreateFlashcard createFlashcard,
    required UpdateFlashcard updateFlashcard,
    required DeleteFlashcard deleteFlashcard,
    required NavigationService navigationService,
  })  : _getDeckById = getDeckById,
        _updateDeck = updateDeck,
        _deleteDeck = deleteDeck,
        _createFlashcard = createFlashcard,
        _updateFlashcard = updateFlashcard,
        _deleteFlashcard = deleteFlashcard,
        _navigationService = navigationService;

  Deck? _currentDeck;
  Deck? get currentDeck => _currentDeck;

  List<Flashcard> get flashcards => _currentDeck?.flashcards ?? [];
  int get totalCards => flashcards.length;
  int get totalReviews => flashcards.fold(0, (sum, card) => sum + card.reviewCount);
  
  bool get hasCards => flashcards.isNotEmpty;
  bool get isEmpty => flashcards.isEmpty;

  // Animation states
  bool _isAnimating = false;
  bool get isAnimating => _isAnimating;

  Future<void> loadDeck(String deckId) async {
    setLoading(true);
    clearError();

    try {
      final result = await _getDeckById(GetDeckByIdParams(deckId: deckId));
      result.fold(
        (failure) => setError(failure.message),
        (deck) {
          _currentDeck = deck;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to load deck: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> updateDeckInfo(String name, String description) async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final updatedDeck = _currentDeck!.copyWith(
        name: name,
        description: description,
      );

      final result = await _updateDeck(UpdateDeckParams(deck: updatedDeck));
      result.fold(
        (failure) => setError(failure.message),
        (deck) {
          _currentDeck = deck;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to update deck: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> addFlashcard(String front, String back) async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final result = await _createFlashcard(CreateFlashcardParams(
        deckId: _currentDeck!.id,
        front: front,
        back: back,
      ));

      result.fold(
        (failure) => setError(failure.message),
        (flashcard) {
          // Reload the deck to get updated flashcards
          loadDeck(_currentDeck!.id);
        },
      );
    } catch (e) {
      setError('Failed to add flashcard: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> updateFlashcard(Flashcard flashcard) async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final result = await _updateFlashcard(UpdateFlashcardParams(flashcard: flashcard));
      result.fold(
        (failure) => setError(failure.message),
        (updatedFlashcard) {
          // Reload the deck to get updated flashcards
          loadDeck(_currentDeck!.id);
          _navigationService.showSnackBar('Flashcard updated successfully');
        },
      );
    } catch (e) {
      setError('Failed to update flashcard: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> deleteFlashcardWithConfirmation(String flashcardId) async {
    final confirmed = await _navigationService.showConfirmationDialog(
      title: 'Delete Flashcard',
      content: 'Are you sure you want to delete this flashcard?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (confirmed) {
      await _deleteFlashcardById(flashcardId);
    }
  }

  Future<void> _deleteFlashcardById(String flashcardId) async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final result = await _deleteFlashcard(DeleteFlashcardParams(flashcardId: flashcardId));
      result.fold(
        (failure) => setError(failure.message),
        (_) {
          // Reload the deck to get updated flashcards
          loadDeck(_currentDeck!.id);
          _navigationService.showSnackBar('Flashcard deleted successfully');
        },
      );
    } catch (e) {
      setError('Failed to delete flashcard: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> deleteDeck() async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final result = await _deleteDeck(DeleteDeckParams(deckId: _currentDeck!.id));
      result.fold(
        (failure) => setError(failure.message),
        (_) {
          // Deck deleted successfully
          _currentDeck = null;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to delete deck: $e');
    } finally {
      setLoading(false);
    }
  }

  void setAnimating(bool animating) {
    _isAnimating = animating;
    notifyListeners();
  }

  // Navigation methods
  bool get canStartStudy => hasCards;
  
  Future<void> navigateToStudy() async {
    if (_currentDeck != null && canStartStudy) {
      await _navigationService.navigateToStudySession(_currentDeck!, StudyMode.review);
    }
  }

  Future<void> navigateToAddFlashcard() async {
    if (_currentDeck != null) {
      await _navigationService.navigateToCreateFlashcard(_currentDeck!);
      // Reload deck after returning from create flashcard
      await loadDeck(_currentDeck!.id);
    }
  }

  void navigateBack() {
    _navigationService.goBack();
  }

  // Business logic methods
  void handleEditFlashcard(Flashcard flashcard) {
    // TODO: Implement edit flashcard functionality
    _navigationService.showSnackBar('Edit flashcard functionality coming soon');
  }

  void handleDeleteFlashcard(Flashcard flashcard) {
    deleteFlashcardWithConfirmation(flashcard.id);
  }

  // Statistics helpers
  double get averageReviewCount {
    if (flashcards.isEmpty) return 0.0;
    return totalReviews / flashcards.length;
  }

  Map<String, int> get difficultyDistribution {
    final distribution = <String, int>{
      'Easy': 0,
      'Medium': 0,
      'Hard': 0,
    };

    for (final card in flashcards) {
      switch (card.difficulty) {
        case 1:
          distribution['Easy'] = distribution['Easy']! + 1;
          break;
        case 2:
          distribution['Medium'] = distribution['Medium']! + 1;
          break;
        case 3:
          distribution['Hard'] = distribution['Hard']! + 1;
          break;
      }
    }

    return distribution;
  }
} 