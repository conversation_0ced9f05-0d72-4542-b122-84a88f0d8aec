import 'dart:async';
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/study_mode.dart';
import '../../domain/entities/study_session.dart';
import '../../domain/usecases/study/get_cards_for_study_mode.dart';
import '../../domain/usecases/study/record_study_result.dart';
import '../../domain/usecases/study/complete_study_session.dart' as complete_use_case;
import '../../domain/usecases/study/start_study_session.dart' as start_use_case;

class StudySessionViewModel extends BaseViewModel {
  final GetCardsForStudyMode _getCardsForStudyMode;
  final RecordStudyResult _recordStudyResult;
  final complete_use_case.CompleteStudySession _completeStudySession;
  final start_use_case.StartStudySession _startStudySession;
  final NavigationService _navigationService;

  StudySessionViewModel({
    required GetCardsForStudyMode getCardsForStudyMode,
    required RecordStudyResult recordStudyResult,
    required complete_use_case.CompleteStudySession completeStudySession,
    required start_use_case.StartStudySession startStudySession,
    required NavigationService navigationService,
  }) : _getCardsForStudyMode = getCardsForStudyMode,
       _recordStudyResult = recordStudyResult,
       _completeStudySession = completeStudySession,
       _startStudySession = startStudySession,
       _navigationService = navigationService;

  // Core state
  StudySession? _currentSession;
  List<Flashcard> _studyCards = [];
  int _currentCardIndex = 0;
  bool _showAnswer = false;
  bool _isSessionActive = false;
  DateTime? _cardStartTime;
  Timer? _sessionTimer;
  Duration _sessionDuration = Duration.zero;

  // Study statistics
  int _correctAnswers = 0;
  int _incorrectAnswers = 0;
  final List<StudyResult> _results = [];
  final Map<String, Duration> _cardResponseTimes = {};

  // Animation states
  bool _isFlipping = false;
  bool _isTransitioning = false;

  // Getters
  StudySession? get currentSession => _currentSession;
  List<Flashcard> get studyCards => _studyCards;
  int get currentCardIndex => _currentCardIndex;
  bool get showAnswer => _showAnswer;
  bool get isSessionActive => _isSessionActive;
  Duration get sessionDuration => _sessionDuration;
  
  Flashcard? get currentCard => 
      _studyCards.isNotEmpty && _currentCardIndex < _studyCards.length 
          ? _studyCards[_currentCardIndex] 
          : null;
  
  bool get hasNextCard => _currentCardIndex < _studyCards.length - 1;
  bool get hasPreviousCard => _currentCardIndex > 0;
  bool get isSessionComplete => _currentCardIndex >= _studyCards.length;
  
  int get totalCards => _studyCards.length;
  int get remainingCards => _studyCards.length - _currentCardIndex;
  double get progress => _studyCards.isEmpty ? 0.0 : ((_currentCardIndex + 1) / _studyCards.length);
  
  // Study statistics getters
  int get correctAnswers => _correctAnswers;
  int get incorrectAnswers => _incorrectAnswers;
  int get totalAnswered => _correctAnswers + _incorrectAnswers;
  double get accuracy => totalAnswered == 0 ? 0.0 : _correctAnswers / totalAnswered;
  
  // Animation state getters
  bool get isFlipping => _isFlipping;
  bool get isTransitioning => _isTransitioning;
  
  // Formatted getters for UI
  String get progressText => '$currentCardIndex / $totalCards';
  String get accuracyText => '${(accuracy * 100).toStringAsFixed(1)}%';
  String get sessionDurationText {
    final minutes = _sessionDuration.inMinutes;
    final seconds = _sessionDuration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Future<void> startSession(Deck deck, StudyMode mode, {int? maxCards}) async {
    setLoading(true);
    clearError();

    try {
      // Get cards for the study mode
      final cardsResult = await _getCardsForStudyMode(
        GetCardsForStudyModeParams(
          cards: deck.flashcards,
          mode: mode,
          maxCards: maxCards,
        ),
      );

      await cardsResult.fold(
        (failure) async {
          setError(failure.message);
        },
        (cards) async {
          if (cards.isEmpty) {
            setError('No cards available for this study mode');
            return;
          }

          _studyCards = cards;
          _currentCardIndex = 0;
          _showAnswer = false;
          _isSessionActive = true;
          _cardStartTime = DateTime.now();
          _correctAnswers = 0;
          _incorrectAnswers = 0;
          _results.clear();
          _cardResponseTimes.clear();
          _sessionDuration = Duration.zero;

          // Start session timer
          _startSessionTimer();

          // Create study session entity
          final sessionResult = await _startStudySession(
            start_use_case.StartStudySessionParams(
              deckId: deck.id,
              cards: cards,
            ),
          );

          sessionResult.fold(
            (failure) => setError(failure.message),
            (session) => _currentSession = session,
          );

          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to start study session: $e');
    } finally {
      setLoading(false);
    }
  }

  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _sessionDuration = _sessionDuration + const Duration(seconds: 1);
      notifyListeners();
    });
  }

  Future<void> toggleAnswer() async {
    if (_isFlipping) return;

    _isFlipping = true;
    notifyListeners();

    // Simulate flip animation delay
    await Future.delayed(const Duration(milliseconds: 300));

    _showAnswer = !_showAnswer;
    _isFlipping = false;
    notifyListeners();
  }

  Future<void> answerCard(bool isCorrect) async {
    if (currentCard == null || _isTransitioning) return;

    _isTransitioning = true;
    notifyListeners();

    try {
      // Calculate response time
      final responseTime = _cardStartTime != null 
          ? DateTime.now().difference(_cardStartTime!)
          : const Duration(seconds: 5);

      // Record the result
      final result = StudyResult(
        cardId: currentCard!.id,
        isCorrect: isCorrect,
        timestamp: DateTime.now(),
        timeSpent: responseTime,
      );

      _results.add(result);
      _cardResponseTimes[currentCard!.id] = responseTime;

      // Update statistics
      if (isCorrect) {
        _correctAnswers++;
      } else {
        _incorrectAnswers++;
      }

      // Record result in repository
      await _recordStudyResult(
        RecordStudyResultParams(
          flashcard: currentCard!,
          isCorrect: isCorrect,
          responseTime: responseTime,
        ),
      );

      // Update session
      if (_currentSession != null) {
        _currentSession = _currentSession!.addResult(result);
      }

      // Move to next card after a brief delay
      await Future.delayed(const Duration(milliseconds: 500));
      await nextCard();

    } catch (e) {
      setError('Failed to record answer: $e');
    } finally {
      _isTransitioning = false;
      notifyListeners();
    }
  }

  Future<void> nextCard() async {
    if (!hasNextCard) {
      await completeSession();
      return;
    }

    _currentCardIndex++;
    _showAnswer = false;
    _cardStartTime = DateTime.now();
    notifyListeners();
  }

  Future<void> previousCard() async {
    if (!hasPreviousCard) return;

    _currentCardIndex--;
    _showAnswer = false;
    _cardStartTime = DateTime.now();
    notifyListeners();
  }

  Future<void> completeSession() async {
    if (_currentSession == null) return;

    setLoading(true);
    
    try {
      // Stop timer
      _sessionTimer?.cancel();
      
      // Complete the session
      final completedSession = _currentSession!.complete();
      
      final result = await _completeStudySession(
        complete_use_case.CompleteStudySessionParams(
          deckId: completedSession.deckId,
          results: _results.map((r) => {
            'cardId': r.cardId,
            'isCorrect': r.isCorrect,
            'timestamp': r.timestamp.toIso8601String(),
            'timeSpent': r.timeSpent.inMilliseconds,
          }).toList(),
          duration: _sessionDuration,
        ),
      );

      result.fold(
        (failure) => setError(failure.message),
        (_) {
          _isSessionActive = false;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to complete session: $e');
    } finally {
      setLoading(false);
    }
  }

  void resetSession() {
    _currentCardIndex = 0;
    _showAnswer = false;
    _cardStartTime = DateTime.now();
    _correctAnswers = 0;
    _incorrectAnswers = 0;
    _results.clear();
    _cardResponseTimes.clear();
    _sessionDuration = Duration.zero;
    
    if (_studyCards.isNotEmpty) {
      _studyCards.shuffle();
    }
    
    _startSessionTimer();
    notifyListeners();
  }

  void endSession() {
    _sessionTimer?.cancel();
    _currentSession = null;
    _studyCards.clear();
    _currentCardIndex = 0;
    _showAnswer = false;
    _isSessionActive = false;
    _cardStartTime = null;
    _correctAnswers = 0;
    _incorrectAnswers = 0;
    _results.clear();
    _cardResponseTimes.clear();
    _sessionDuration = Duration.zero;
    clearError();
    notifyListeners();
  }

  // Navigation methods
  void navigateBack() {
    endSession();
    _navigationService.goBack();
  }

  void navigateToResults() {
    // For now, just navigate back - can be enhanced later with results screen
    _navigationService.goBack();
  }

  @override
  void dispose() {
    _sessionTimer?.cancel();
    super.dispose();
  }
}

// Note: Parameter classes are now imported from their respective use case files 