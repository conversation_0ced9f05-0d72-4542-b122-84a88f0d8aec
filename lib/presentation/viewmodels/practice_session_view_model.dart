
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../domain/entities/flashcard.dart';

class PracticeSessionViewModel extends BaseViewModel {
  final NavigationService _navigationService;

  PracticeSessionViewModel({
    required NavigationService navigationService,
  }) : _navigationService = navigationService;

  late List<Flashcard> _sessionCards;
  final Map<String, int> _cardScores = {};
  int _currentIndex = 0;
  int _correctAnswers = 0;
  int _totalAnswered = 0;
  bool _sessionComplete = false;
  DateTime? _sessionStartTime;

  // Getters
  List<Flashcard> get sessionCards => _sessionCards;
  int get currentIndex => _currentIndex;
  int get correctAnswers => _correctAnswers;
  int get totalAnswered => _totalAnswered;
  bool get sessionComplete => _sessionComplete;
  DateTime? get sessionStartTime => _sessionStartTime;
  
  double get progress => _sessionCards.isEmpty ? 0.0 : (_currentIndex / _sessionCards.length);
  double get accuracy => _totalAnswered == 0 ? 0.0 : (_correctAnswers / _totalAnswered);
  
  Flashcard? get currentCard => _sessionCards.isNotEmpty && _currentIndex < _sessionCards.length 
      ? _sessionCards[_currentIndex] 
      : null;

  void initializeSession(List<Flashcard> cards) {
    _sessionCards = List.from(cards)..shuffle();
    _sessionStartTime = DateTime.now();
    _currentIndex = 0;
    _correctAnswers = 0;
    _totalAnswered = 0;
    _sessionComplete = false;
    _cardScores.clear();
    
    // Initialize scores for spaced repetition
    for (final card in _sessionCards) {
      _cardScores[card.id] = 0;
    }
    
    notifyListeners();
  }

  void answerCard(bool isCorrect) {
    if (_sessionComplete || currentCard == null) return;

    _totalAnswered++;
    if (isCorrect) {
      _correctAnswers++;
      _cardScores[currentCard!.id] = (_cardScores[currentCard!.id] ?? 0) + 1;
    }

    _moveToNextCard();
  }

  void _moveToNextCard() {
    if (_currentIndex < _sessionCards.length - 1) {
      _currentIndex++;
    } else {
      _completeSession();
    }
    notifyListeners();
  }

  void _completeSession() {
    _sessionComplete = true;
    notifyListeners();
  }

  void restartSession() {
    _currentIndex = 0;
    _correctAnswers = 0;
    _totalAnswered = 0;
    _sessionComplete = false;
    _sessionStartTime = DateTime.now();
    _sessionCards.shuffle();
    _cardScores.clear();
    
    for (final card in _sessionCards) {
      _cardScores[card.id] = 0;
    }
    
    notifyListeners();
  }

  void navigateBack() {
    _navigationService.goBack();
  }

  void showSessionStats() {
    final duration = _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!)
        : Duration.zero;
    
    final message = '''
Session Complete!
Cards: ${_sessionCards.length}
Correct: $_correctAnswers
Accuracy: ${(accuracy * 100).toStringAsFixed(1)}%
Time: ${duration.inMinutes}m ${duration.inSeconds % 60}s
''';
    
    _navigationService.showSnackBar(message);
  }
} 