
import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/study_mode.dart';
import '../../domain/usecases/study/update_deck_last_studied.dart';

class StudyViewModel extends BaseViewModel {
  final UpdateDeckLastStudied _updateDeckLastStudied;
  final NavigationService _navigationService;

  StudyViewModel({
    required UpdateDeckLastStudied updateDeckLastStudied,
    required NavigationService navigationService,
  }) : _updateDeckLastStudied = updateDeckLastStudied,
       _navigationService = navigationService;

  Deck? _currentDeck;
  StudyMode _studyMode = StudyMode.review;
  List<Flashcard> _studyCards = [];
  int _currentCardIndex = 0;
  bool _showAnswer = false;
  
  // Getters
  Deck? get currentDeck => _currentDeck;
  StudyMode get studyMode => _studyMode;
  List<Flashcard> get studyCards => _studyCards;
  int get currentCardIndex => _currentCardIndex;
  bool get showAnswer => _showAnswer;
  
  Flashcard? get currentCard => 
      _studyCards.isNotEmpty && _currentCardIndex < _studyCards.length 
          ? _studyCards[_currentCardIndex] 
          : null;
  
  bool get hasNextCard => _currentCardIndex < _studyCards.length - 1;
  bool get hasPreviousCard => _currentCardIndex > 0;
  bool get isStudyComplete => _currentCardIndex >= _studyCards.length;
  
  int get totalCards => _studyCards.length;
  int get remainingCards => _studyCards.length - _currentCardIndex;
  double get progress => _studyCards.isEmpty ? 0.0 : ((_currentCardIndex + 1) / _studyCards.length).toDouble();

  // Check if deck has cards for studying
  bool get hasCards => _currentDeck?.flashcards.isNotEmpty ?? false;

  void startStudySession(Deck deck, StudyMode mode) {
    _currentDeck = deck;
    _studyMode = mode;
    _studyCards = getCardsForMode(deck.flashcards, mode);
    _currentCardIndex = 0;
    _showAnswer = false;
    notifyListeners();
  }

  List<Flashcard> getCardsForMode(List<Flashcard> allCards, StudyMode mode) {
    switch (mode) {
      case StudyMode.review:
        return List.from(allCards)..shuffle();
      case StudyMode.practice:
        return List.from(allCards)..shuffle();
      case StudyMode.test:
        return List.from(allCards)..shuffle();
      case StudyMode.learn:
        // Return cards that haven't been reviewed much
        return allCards.where((card) => card.reviewCount < 3).toList()..shuffle();
      case StudyMode.weakCards:
        // Return cards with low success rate (for now, just cards with high review count)
        return allCards.where((card) => card.reviewCount > 5).toList()..shuffle();
    }
  }

  void toggleAnswer() {
    _showAnswer = !_showAnswer;
    notifyListeners();
  }

  void nextCard() {
    if (hasNextCard) {
      _currentCardIndex++;
      _showAnswer = false;
      notifyListeners();
    }
  }

  void previousCard() {
    if (hasPreviousCard) {
      _currentCardIndex--;
      _showAnswer = false;
      notifyListeners();
    }
  }

  void markCardDifficulty(double difficulty) {
    if (currentCard != null) {
      // Update card difficulty and review count
      final updatedCard = currentCard!.copyWith(
        difficulty: difficulty,
        reviewCount: currentCard!.reviewCount + 1,
        lastReviewed: DateTime.now(),
      );
      
      // Update the card in the study list
      _studyCards[_currentCardIndex] = updatedCard;
      
      // Move to next card automatically
      nextCard();
    }
  }

  Future<void> completeStudySession() async {
    if (_currentDeck == null) return;

    setLoading(true);
    clearError();

    try {
      final result = await _updateDeckLastStudied(
        UpdateDeckLastStudiedParams(deck: _currentDeck!),
      );

      result.fold(
        (failure) => setError(failure.message),
        (updatedDeck) {
          _currentDeck = updatedDeck;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to update study session: $e');
    } finally {
      setLoading(false);
    }
  }

  void resetSession() {
    _currentCardIndex = 0;
    _showAnswer = false;
    if (_studyCards.isNotEmpty) {
      _studyCards.shuffle();
    }
    notifyListeners();
  }

  void endSession() {
    _currentDeck = null;
    _studyCards.clear();
    _currentCardIndex = 0;
    _showAnswer = false;
    clearError();
    notifyListeners();
  }

  // Navigation methods
  void navigateBack() {
    _navigationService.goBack();
  }

  Future<void> navigateToStudySession(Deck deck, StudyMode mode) async {
    await _navigationService.navigateToStudySession(deck, mode);
  }

  // Method that was referenced in the view
  void setDeck(Deck deck) {
    _currentDeck = deck;
    _studyCards = deck.flashcards;
    notifyListeners();
  }

  // Getter that was referenced in the view
  String get lastStudiedText {
    if (_currentDeck?.lastStudied == null) {
      return 'Never';
    }
    final lastStudied = _currentDeck!.lastStudied!;
    final now = DateTime.now();
    final difference = now.difference(lastStudied);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
} 
