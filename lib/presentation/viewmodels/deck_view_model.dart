import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/usecases/deck/get_decks.dart';
import '../../domain/usecases/deck/create_deck.dart';
import '../../domain/usecases/deck/update_deck.dart';
import '../../domain/usecases/deck/delete_deck.dart';

class DeckViewModel extends BaseViewModel {
  final GetDecks _getDecks;
  final CreateDeck _createDeck;
  final UpdateDeck _updateDeck;
  final DeleteDeck _deleteDeck;
  final NavigationService _navigationService;

  List<Deck> _decks = [];
  List<Deck> get decks => _decks;

  DeckViewModel({
    required GetDecks getDecks,
    required CreateDeck createDeck,
    required UpdateDeck updateDeck,
    required DeleteDeck deleteDeck,
    required NavigationService navigationService,
  })  : _getDecks = getDecks,
        _createDeck = createDeck,
        _updateDeck = updateDeck,
        _deleteDeck = deleteDeck,
        _navigationService = navigationService;

  Future<void> loadDecks() async {
    setLoading(true);
    clearError();

    final result = await _getDecks(const NoParams());
    result.fold(
      (failure) {
        setError('Failed to load decks: ${failure.message}');
      },
      (decks) {
        _decks = decks;
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> createDeck(String name, String description) async {
    setLoading(true);
    clearError();

    final result = await _createDeck(CreateDeckParams(
      name: name,
      description: description,
    ));

    result.fold(
      (failure) {
        setError('Failed to create deck: ${failure.message}');
      },
      (deck) {
        _decks.add(deck);
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> updateDeck(Deck deck) async {
    setLoading(true);
    clearError();

    final result = await _updateDeck(UpdateDeckParams(deck: deck));

    result.fold(
      (failure) => setError('Failed to update deck: ${failure.message}'),
      (updatedDeck) {
        final index = _decks.indexWhere((d) => d.id == updatedDeck.id);
        if (index != -1) {
          _decks[index] = updatedDeck;
          notifyListeners();
        }
      },
    );

    setLoading(false);
  }

  Future<void> deleteDeck(String deckId) async {
    setLoading(true);
    clearError();

    final result = await _deleteDeck(DeleteDeckParams(deckId: deckId));

    result.fold(
      (failure) => setError('Failed to delete deck: ${failure.message}'),
      (_) {
        _decks.removeWhere((deck) => deck.id == deckId);
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> addFlashcardToDeck(String deckId, Flashcard flashcard) async {
    final deckIndex = _decks.indexWhere((deck) => deck.id == deckId);
    if (deckIndex != -1) {
      final updatedDeck = _decks[deckIndex].addFlashcard(flashcard);
      await updateDeck(updatedDeck);
    }
  }

  Future<void> updateFlashcardInDeck(String deckId, Flashcard flashcard) async {
    final deckIndex = _decks.indexWhere((deck) => deck.id == deckId);
    if (deckIndex != -1) {
      final updatedDeck = _decks[deckIndex].updateFlashcard(flashcard);
      await updateDeck(updatedDeck);
    }
  }

  Future<void> removeFlashcardFromDeck(String deckId, String flashcardId) async {
    final deckIndex = _decks.indexWhere((deck) => deck.id == deckId);
    if (deckIndex != -1) {
      final updatedDeck = _decks[deckIndex].removeFlashcard(flashcardId);
      await updateDeck(updatedDeck);
    }
  }

  Deck? getDeckById(String deckId) {
    try {
      return _decks.firstWhere((deck) => deck.id == deckId);
    } catch (e) {
      return null;
    }
  }

  // Navigation methods
  Future<void> navigateToDeckDetail(Deck deck) async {
    await _navigationService.navigateToDeckDetail(deck);
  }
} 