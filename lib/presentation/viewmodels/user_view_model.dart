import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/user/get_current_user.dart';
import '../../domain/usecases/user/login_user.dart';
import '../../domain/usecases/user/register_user.dart';
import '../../domain/usecases/user/logout_user.dart';

class UserViewModel extends BaseViewModel {
  final GetCurrentUser _getCurrentUser;
  final LoginUser _loginUser;
  final RegisterUser _registerUser;
  final LogoutUser _logoutUser;
  final NavigationService _navigationService;

  User? _currentUser;
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;

  UserViewModel({
    required GetCurrentUser getCurrentUser,
    required LoginUser loginUser,
    required RegisterUser registerUser,
    required LogoutUser logoutUser,
    required NavigationService navigationService,
  })  : _getCurrentUser = getCurrentUser,
        _loginUser = loginUser,
        _registerUser = registerUser,
        _logoutUser = logoutUser,
        _navigationService = navigationService;

  Future<void> loadUserState() async {
    setLoading(true);
    clearError();

    final result = await _getCurrentUser(const NoParams());
    result.fold(
      (failure) => setError('Failed to load user: ${failure.message}'),
      (user) {
        _currentUser = user;
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> login(String email, String password) async {
    setLoading(true);
    clearError();

    final result = await _loginUser(LoginUserParams(
      email: email,
      password: password,
    ));

    result.fold(
      (failure) => setError('Login failed: ${failure.message}'),
      (user) {
        _currentUser = user;
        notifyListeners();
        _navigationService.navigateToMainNavigation();
      },
    );

    setLoading(false);
  }

  Future<void> register(String name, String email, String password) async {
    setLoading(true);
    clearError();

    final result = await _registerUser(RegisterUserParams(
      name: name,
      email: email,
      password: password,
    ));

    result.fold(
      (failure) => setError('Registration failed: ${failure.message}'),
      (user) {
        _currentUser = user;
        notifyListeners();
        _navigationService.navigateToMainNavigation();
      },
    );

    setLoading(false);
  }

  Future<void> logout() async {
    setLoading(true);
    clearError();

    final result = await _logoutUser(const NoParams());
    result.fold(
      (failure) => setError('Logout failed: ${failure.message}'),
      (_) {
        _currentUser = null;
        notifyListeners();
      },
    );

    setLoading(false);
  }

  // Alias for logout to maintain compatibility
  Future<void> signOut() async {
    await logout();
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? avatarUrl,
  }) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.copyWith(
      name: name,
      email: email,
      avatarUrl: avatarUrl,
    );

    notifyListeners();
  }

  Future<void> updatePreferences(Map<String, dynamic> newPreferences) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.updatePreferences(newPreferences);
    notifyListeners();
  }

  Future<void> incrementStudyStats({
    int cardsStudied = 0,
    bool maintainStreak = false,
  }) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.incrementStudyStats(
      cardsStudied: cardsStudied,
      maintainStreak: maintainStreak,
    );

    notifyListeners();
  }

  bool getPreference(String key, {bool defaultValue = false}) {
    return _currentUser?.getBoolPreference(key, defaultValue: defaultValue) ?? defaultValue;
  }

  T? getPreferenceValue<T>(String key, {T? defaultValue}) {
    return _currentUser?.getPreference<T>(key, defaultValue: defaultValue);
  }
} 