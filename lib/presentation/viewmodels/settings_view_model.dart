import 'package:flutter/material.dart';

import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/settings.dart';
import '../../domain/usecases/settings/get_settings.dart';
import '../../domain/usecases/settings/update_settings.dart';
import '../../domain/usecases/theme/get_theme_mode.dart';
import '../../domain/usecases/theme/set_theme_mode.dart';

class AppSettings {
  final bool isDarkMode;
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final int studyReminderHour;
  final int studyReminderMinute;

  AppSettings({
    required this.isDarkMode,
    required this.notificationsEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.studyReminderHour,
    required this.studyReminderMinute,
  });

  AppSettings copyWith({
    bool? isDarkMode,
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    int? studyReminderHour,
    int? studyReminderMinute,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      studyReminderHour: studyReminderHour ?? this.studyReminderHour,
      studyReminderMinute: studyReminderMinute ?? this.studyReminderMinute,
    );
  }
}

class SettingsViewModel extends BaseViewModel {
  final GetSettings _getSettings;
  final UpdateSettings _updateSettings;
  final SetThemeMode _setThemeMode;
  final NavigationService _navigationService;

  SettingsViewModel({
    required GetSettings getSettings,
    required UpdateSettings updateSettings,
    required GetThemeMode getThemeMode,
    required SetThemeMode setThemeMode,
    required NavigationService navigationService,
  })  : _getSettings = getSettings,
        _updateSettings = updateSettings,
        _setThemeMode = setThemeMode,
        _navigationService = navigationService;

  Settings _settings = const Settings();
  Settings get settings => _settings;

  // Individual getters for easy access
  ThemeMode get themeMode => _settings.themeMode;
  bool get notificationsEnabled => _settings.notificationsEnabled;
  bool get soundEnabled => _settings.soundEnabled;
  bool get vibrationEnabled => _settings.vibrationEnabled;
  String get language => _settings.language;
  int get studyReminder => _settings.studyReminder;
  bool get autoAdvance => _settings.autoAdvance;
  int get cardDisplayTime => _settings.cardDisplayTime;

  Future<void> loadSettings() async {
    setLoading(true);
    clearError();

    try {
      final result = await _getSettings(NoParams());
      result.fold(
        (failure) => setError(failure.message),
        (settings) {
          _settings = settings;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to load settings: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> updateThemeMode(ThemeMode themeMode) async {
    setLoading(true);
    clearError();

    try {
      // Update theme mode through theme use case
      final themeResult = await _setThemeMode(SetThemeModeParams(themeMode: themeMode));
      
      await themeResult.fold(
        (failure) async => setError(failure.message),
        (_) async {
          // Update settings
          final updatedSettings = _settings.copyWith(themeMode: themeMode);
          final settingsResult = await _updateSettings(UpdateSettingsParams(settings: updatedSettings));
          
          settingsResult.fold(
            (failure) => setError(failure.message),
            (settings) {
              _settings = settings;
              notifyListeners();
            },
          );
        },
      );
    } catch (e) {
      setError('Failed to update theme: $e');
    } finally {
      setLoading(false);
    }
  }

  void selectThemeModeAndClose(ThemeMode themeMode) {
    updateThemeMode(themeMode);
    _navigationService.goBack();
  }

  Future<void> updateNotifications(bool enabled) async {
    await _updateSetting(_settings.copyWith(notificationsEnabled: enabled));
  }

  Future<void> updateSound(bool enabled) async {
    await _updateSetting(_settings.copyWith(soundEnabled: enabled));
  }

  Future<void> updateVibration(bool enabled) async {
    await _updateSetting(_settings.copyWith(vibrationEnabled: enabled));
  }

  Future<void> updateLanguage(String language) async {
    await _updateSetting(_settings.copyWith(language: language));
  }

  Future<void> updateStudyReminder(int hours) async {
    await _updateSetting(_settings.copyWith(studyReminder: hours));
  }

  Future<void> updateAutoAdvance(bool enabled) async {
    await _updateSetting(_settings.copyWith(autoAdvance: enabled));
  }

  Future<void> updateCardDisplayTime(int seconds) async {
    await _updateSetting(_settings.copyWith(cardDisplayTime: seconds));
  }

  Future<void> _updateSetting(Settings newSettings) async {
    setLoading(true);
    clearError();

    try {
      final result = await _updateSettings(UpdateSettingsParams(settings: newSettings));
      result.fold(
        (failure) => setError(failure.message),
        (settings) {
          _settings = settings;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to update setting: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> resetToDefaults() async {
    setLoading(true);
    clearError();

    try {
      const defaultSettings = Settings();
      final result = await _updateSettings(UpdateSettingsParams(settings: defaultSettings));
      result.fold(
        (failure) => setError(failure.message),
        (settings) {
          _settings = settings;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to reset settings: $e');
    } finally {
      setLoading(false);
    }
  }
} 