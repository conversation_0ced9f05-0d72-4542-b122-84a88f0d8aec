import '../../../core/viewmodels/base_view_model.dart';
import '../../../core/services/navigation_service.dart';
import '../../../domain/entities/user.dart';
import '../../../domain/usecases/user/login_user.dart';
import '../../../domain/usecases/user/forgot_password.dart';

class LoginViewModel extends BaseViewModel {
  final LoginUser _loginUser;
  final ForgotPassword _forgotPassword;
  final NavigationService _navigationService;

  User? _currentUser;
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;

  LoginViewModel({
    required LoginUser loginUser,
    required ForgotPassword forgotPassword,
    required NavigationService navigationService,
  })  : _loginUser = loginUser,
        _forgotPassword = forgotPassword,
        _navigationService = navigationService;

  Future<void> login(String email, String password) async {
    setLoading(true);
    clearError();

    final result = await _loginUser(LoginUserParams(
      email: email,
      password: password,
    ));

    result.fold(
      (failure) => setError('Login failed: ${failure.message}'),
      (user) {
        _currentUser = user;
        notifyListeners();
        _navigationService.navigateToMainNavigation();
      },
    );

    setLoading(false);
  }

  Future<void> forgotPassword(String email) async {
    setLoading(true);
    clearError();

    final result = await _forgotPassword(ForgotPasswordParams(email: email));

    result.fold(
      (failure) => setError('Failed to send reset email: ${failure.message}'),
      (_) {
        notifyListeners();
        _navigationService.showSnackBar('Password reset email sent successfully');
      },
    );

    setLoading(false);
  }

  void navigateToSignup() {
    _navigationService.navigateToSignup();
  }

  void navigateToForgotPassword() {
    _navigationService.navigateToForgotPassword();
  }

  void navigateBack() {
    _navigationService.goBack();
  }
} 