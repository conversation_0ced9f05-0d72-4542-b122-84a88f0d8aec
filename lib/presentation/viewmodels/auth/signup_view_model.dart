import '../../../core/viewmodels/base_view_model.dart';
import '../../../core/services/navigation_service.dart';
import '../../../domain/entities/user.dart';
import '../../../domain/usecases/user/register_user.dart';
import '../../../domain/usecases/user/verify_email.dart';
import '../../../domain/usecases/user/resend_verification_email.dart';

enum SignupStep {
  basicInfo,
  verification,
  completed,
}

class SignupViewModel extends BaseViewModel {
  final RegisterUser _registerUser;
  final VerifyEmail _verifyEmail;
  final ResendVerificationEmail _resendVerificationEmail;
  final NavigationService _navigationService;

  SignupStep _currentStep = SignupStep.basicInfo;
  SignupStep get currentStep => _currentStep;

  String _email = '';
  String get email => _email;

  bool _emailExists = false;
  bool get emailExists => _emailExists;

  bool _isVerificationEmailSent = false;
  bool get isVerificationEmailSent => _isVerificationEmailSent;

  User? _currentUser;
  User? get currentUser => _currentUser;

  SignupViewModel({
    required RegisterUser registerUser,
    required VerifyEmail verifyEmail,
    required ResendVerificationEmail resendVerificationEmail,
    required NavigationService navigationService,
  })  : _registerUser = registerUser,
        _verifyEmail = verifyEmail,
        _resendVerificationEmail = resendVerificationEmail,
        _navigationService = navigationService;

  Future<void> register(String name, String email, String password) async {
    setLoading(true);
    clearError();
    _email = email;

    final result = await _registerUser(RegisterUserParams(
      name: name,
      email: email,
      password: password,
    ));

    result.fold(
      (failure) {
        if (failure.message.toLowerCase().contains('email already exists')) {
          _emailExists = true;
          setError('An account with this email already exists');
        } else {
          setError('Registration failed: ${failure.message}');
        }
      },
      (user) {
        _currentUser = user;
        _currentStep = SignupStep.verification;
        _isVerificationEmailSent = true;
        notifyListeners();
      },
    );

    setLoading(false);
  }

  Future<void> verifyEmail(String verificationCode) async {
    setLoading(true);
    clearError();

    final result = await _verifyEmail(VerifyEmailParams(
      email: _email,
      verificationCode: verificationCode,
    ));

    result.fold(
      (failure) => setError('Verification failed: ${failure.message}'),
      (_) {
        _currentStep = SignupStep.completed;
        notifyListeners();
        _navigationService.navigateToMainNavigation();
      },
    );

    setLoading(false);
  }

  Future<void> resendVerificationEmail() async {
    setLoading(true);
    clearError();

    final result = await _resendVerificationEmail(
      ResendVerificationEmailParams(email: _email),
    );

    result.fold(
      (failure) => setError('Failed to resend email: ${failure.message}'),
      (_) {
        _isVerificationEmailSent = true;
        notifyListeners();
        _navigationService.showSnackBar('Verification email sent successfully');
      },
    );

    setLoading(false);
  }

  void resetSignup() {
    _currentStep = SignupStep.basicInfo;
    _email = '';
    _emailExists = false;
    _isVerificationEmailSent = false;
    _currentUser = null;
    clearError();
    notifyListeners();
  }

  void goToPreviousStep() {
    switch (_currentStep) {
      case SignupStep.verification:
        _currentStep = SignupStep.basicInfo;
        break;
      case SignupStep.completed:
        _currentStep = SignupStep.verification;
        break;
      case SignupStep.basicInfo:
        navigateBack();
        return;
    }
    notifyListeners();
  }

  void navigateToLogin() {
    _navigationService.navigateToLogin();
  }

  void navigateBack() {
    _navigationService.goBack();
  }

  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool isValidPassword(String password) {
    return password.length >= 8 && 
           password.contains(RegExp(r'[A-Za-z]')) &&
           password.contains(RegExp(r'[0-9]'));
  }

  String? validateName(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter your name';
    }
    if (value!.length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter your email';
    }
    if (!isValidEmail(value!)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Please enter a password';
    }
    if (value!.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!value.contains(RegExp(r'[A-Za-z]'))) {
      return 'Password must contain at least one letter';
    }
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    return null;
  }

  String? validateConfirmPassword(String? value, String password) {
    if (value?.isEmpty ?? true) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }
} 