import '../../core/viewmodels/base_view_model.dart';
import '../../core/services/navigation_service.dart';

class MainNavigationViewModel extends BaseViewModel {
  final NavigationService _navigationService;

  MainNavigationViewModel({
    required NavigationService navigationService,
  }) : _navigationService = navigationService;

  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  void onTabTapped(int index) {
    if (index == _currentIndex) return;
    
    _currentIndex = index;
    notifyListeners();
  }

  Future<bool?> navigateToCreateDeck() async {
    return await _navigationService.navigateToCreateDeck();
  }
} 