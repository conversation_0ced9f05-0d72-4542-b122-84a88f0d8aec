import '../../core/viewmodels/base_view_model.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/deck.dart';
import '../../domain/usecases/deck/get_decks.dart';

class AnalyticsViewModel extends BaseViewModel {
  final GetDecks _getDecks;

  AnalyticsViewModel({
    required GetDecks getDecks,
  }) : _getDecks = getDecks;

  List<Deck> _decks = [];
  List<Deck> get decks => _decks;

  // Analytics data
  int get totalDecks => _decks.length;
  int get totalCards => _decks.fold(0, (sum, deck) => sum + deck.flashcards.length);
  int get totalReviews => _decks.fold(0, (sum, deck) => 
      sum + deck.flashcards.fold(0, (cardSum, card) => cardSum + card.reviewCount));

  double get averageCardsPerDeck {
    if (_decks.isEmpty) return 0.0;
    return totalCards / _decks.length;
  }

  double get averageReviewsPerCard {
    if (totalCards == 0) return 0.0;
    return totalReviews / totalCards;
  }

  Map<String, int> get deckSizeDistribution {
    final distribution = <String, int>{
      'Small (1-10)': 0,
      'Medium (11-25)': 0,
      'Large (26-50)': 0,
      'Extra Large (50+)': 0,
    };

    for (final deck in _decks) {
      final cardCount = deck.flashcards.length;
      if (cardCount <= 10) {
        distribution['Small (1-10)'] = distribution['Small (1-10)']! + 1;
      } else if (cardCount <= 25) {
        distribution['Medium (11-25)'] = distribution['Medium (11-25)']! + 1;
      } else if (cardCount <= 50) {
        distribution['Large (26-50)'] = distribution['Large (26-50)']! + 1;
      } else {
        distribution['Extra Large (50+)'] = distribution['Extra Large (50+)']! + 1;
      }
    }

    return distribution;
  }

  Future<void> loadAnalytics() async {
    setLoading(true);
    clearError();

    try {
      final result = await _getDecks(NoParams());
      result.fold(
        (failure) => setError(failure.message),
        (decks) {
          _decks = decks;
          notifyListeners();
        },
      );
    } catch (e) {
      setError('Failed to load analytics: $e');
    } finally {
      setLoading(false);
    }
  }

  void refresh() {
    loadAnalytics();
  }
} 