import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import '../../../domain/entities/flashcard.dart';
import '../../../core/theme/app_theme.dart';
import 'shadcn_button_widget.dart';

/// Migrated FlashCard widget using shadcn_flutter components
/// Maintains the same functionality while using shadcn design system
class ShadcnFlashCardWidget extends StatefulWidget {
  final Flashcard flashCard;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final VoidCallback? onShuffle;
  final bool showNavigationButtons;
  final int? currentIndex;
  final int? totalCards;
  final Function(bool isCorrect)? onAnswer;

  const ShadcnFlashCardWidget({
    super.key,
    required this.flashCard,
    this.onNext,
    this.onPrevious,
    this.onShuffle,
    this.showNavigationButtons = true,
    this.currentIndex,
    this.totalCards,
    this.onAnswer,
  });

  @override
  State<ShadcnFlashCardWidget> createState() => _ShadcnFlashCardWidgetState();
}

class _ShadcnFlashCardWidgetState extends State<ShadcnFlashCardWidget>
    with TickerProviderStateMixin {
  bool _isFlipped = false;
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;

  @override
  void initState() {
    super.initState();
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _flipController.dispose();
    super.dispose();
  }

  void _flipCard() {
    setState(() => _isFlipped = !_isFlipped);
    HapticFeedback.lightImpact();
    
    if (_isFlipped) {
      _flipController.forward();
    } else {
      _flipController.reverse();
    }
  }

  void _handleSwipe(DismissDirection direction) {
    HapticFeedback.mediumImpact();
    
    if (direction == DismissDirection.endToStart && widget.onNext != null) {
      widget.onNext!();
    } else if (direction == DismissDirection.startToEnd && widget.onPrevious != null) {
      widget.onPrevious!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Container(
      height: size.height * 0.7,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (widget.showNavigationButtons) _buildHeader(theme),
          const SizedBox(height: 16),
          Expanded(child: _buildCard(theme, size)),
          const SizedBox(height: 16),
          if (widget.showNavigationButtons) _buildFooter(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildHeaderButton(
          icon: PhosphorIcons.shuffle(),
          onTap: widget.onShuffle,
          theme: theme,
        ),
        if (widget.currentIndex != null && widget.totalCards != null)
          Badge(
            child: Text(
              '${widget.currentIndex! + 1} / ${widget.totalCards}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        _buildHeaderButton(
          icon: _isFlipped ? PhosphorIcons.eye() : PhosphorIcons.eyeSlash(),
          onTap: _flipCard,
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback? onTap,
    required ThemeData theme,
  }) {
    return Button(
      onPressed: onTap,
      variant: ButtonVariant.ghost,
      size: ButtonSize.sm,
      child: Icon(
        icon,
        size: 20,
      ),
    );
  }

  Widget _buildCard(ThemeData theme, Size size) {
    return Dismissible(
      key: ValueKey(widget.flashCard.id),
      direction: DismissDirection.horizontal,
      onDismissed: _handleSwipe,
      resizeDuration: null,
      child: GestureDetector(
        onTap: _flipCard,
        child: AnimatedBuilder(
          animation: _flipAnimation,
          builder: (context, child) {
            final isShowingFront = _flipAnimation.value < 0.5;
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(_flipAnimation.value * 3.14159),
              child: isShowingFront
                  ? _buildCardSide(
                      content: widget.flashCard.front,
                      label: 'Question',
                      gradient: AppTheme.primaryGradient,
                      theme: theme,
                      size: size,
                    )
                  : Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()..rotateY(3.14159),
                      child: _buildCardSide(
                        content: widget.flashCard.back,
                        label: 'Answer',
                        gradient: AppTheme.secondaryGradient,
                        theme: theme,
                        size: size,
                      ),
                    ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCardSide({
    required String content,
    required String label,
    required List<Color> gradient,
    required ThemeData theme,
    required Size size,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient.map((c) => c.withValues(alpha: 0.8)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: gradient.first.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Background pattern
            Positioned.fill(
              child: CustomPaint(
                painter: ShadcnPatternPainter(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Label
                  Badge(
                    variant: BadgeVariant.secondary,
                    child: Text(
                      label,
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Content
                  Expanded(
                    child: Center(
                      child: SingleChildScrollView(
                        child: Text(
                          content,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  
                  // Tap hint
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        PhosphorIcons.hand(),
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tap to flip',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(ThemeData theme) {
    if (widget.onAnswer == null) return const SizedBox.shrink();
    
    return Row(
      children: [
        Expanded(
          child: ShadcnButtonWidget(
            text: 'Hard',
            icon: PhosphorIcons.xCircle(),
            variant: ShadcnButtonVariant.destructive,
            onPressed: () => widget.onAnswer!(false),
            isExpanded: true,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ShadcnButtonWidget(
            text: 'Easy',
            icon: PhosphorIcons.checkCircle(),
            variant: ShadcnButtonVariant.primary,
            onPressed: () => widget.onAnswer!(true),
            isExpanded: true,
          ),
        ),
      ],
    );
  }
}

/// Custom painter for background pattern using shadcn design principles
class ShadcnPatternPainter extends CustomPainter {
  final Color color;

  ShadcnPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const spacing = 40.0;

    // Draw diagonal lines
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
