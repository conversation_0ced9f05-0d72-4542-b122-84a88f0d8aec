import 'package:flutter/material.dart' hide Tooltip;
import 'package:flutter/services.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

/// A shadcn-based button widget that replaces our custom AnimatedButton
/// while maintaining the same API and visual consistency
class ShadcnButtonWidget extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ShadcnButtonVariant variant;
  final bool isLoading;
  final bool isExpanded;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool hapticFeedback;

  const ShadcnButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.variant = ShadcnButtonVariant.primary,
    this.isLoading = false,
    this.isExpanded = false,
    this.padding,
    this.width,
    this.height,
    this.hapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget buttonWidget = _buildShadcnButton();

    // Apply width constraints if specified
    if (width != null || isExpanded) {
      buttonWidget = SizedBox(
        width: isExpanded ? double.infinity : width,
        height: height,
        child: buttonWidget,
      );
    }

    // Apply padding if specified
    if (padding != null) {
      buttonWidget = Padding(
        padding: padding!,
        child: buttonWidget,
      );
    }

    return buttonWidget;
  }

  void _handlePress() {
    if (hapticFeedback) {
      HapticFeedback.selectionClick();
    }
    onPressed?.call();
  }

  Widget _buildShadcnButton() {
    Widget child = _buildButtonContent();

    switch (variant) {
      case ShadcnButtonVariant.primary:
        return PrimaryButton(
          onPressed: isLoading ? null : _handlePress,
          child: child,
        );
      case ShadcnButtonVariant.secondary:
        return SecondaryButton(
          onPressed: isLoading ? null : _handlePress,
          child: child,
        );
      case ShadcnButtonVariant.outline:
        return OutlineButton(
          onPressed: isLoading ? null : _handlePress,
          child: child,
        );
      case ShadcnButtonVariant.ghost:
        return GhostButton(
          onPressed: isLoading ? null : _handlePress,
          child: child,
        );
      case ShadcnButtonVariant.destructive:
        return DestructiveButton(
          onPressed: isLoading ? null : _handlePress,
          child: child,
        );
    }
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: Progress(),
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    return Text(text);
  }
}

/// Button variant enum that matches our existing design system
enum ShadcnButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  destructive,
}

/// Floating Action Button widget using shadcn design principles
class ShadcnFloatingActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final List<Color>? gradient;
  final bool mini;
  final String? tooltip;

  const ShadcnFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.gradient,
    this.mini = false,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    Widget fab = PrimaryButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      density: mini ? ButtonDensity.icon : ButtonDensity.comfortable,
      child: Icon(
        icon,
        size: mini ? 16 : 20,
      ),
    );

    // Apply gradient decoration if specified
    if (gradient != null) {
      fab = Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient!,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(mini ? 20 : 28),
          boxShadow: [
            BoxShadow(
              color: gradient!.first.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: const Color(0x00000000), // transparent
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              onPressed();
            },
            borderRadius: BorderRadius.circular(mini ? 20 : 28),
            child: Container(
              width: mini ? 40 : 56,
              height: mini ? 40 : 56,
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: const Color(0xFFFFFFFF), // white
                size: mini ? 16 : 20,
              ),
            ),
          ),
        ),
      );
    }

    // Add tooltip if specified
    if (tooltip != null) {
      fab = Tooltip(
        tooltip: (context) => Text(tooltip!),
        child: fab,
      );
    }

    return fab;
  }
}
