import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import '../../../core/theme/shadcn_theme.dart';

/// A shadcn-based button widget that replaces our custom AnimatedButton
/// while maintaining the same API and visual consistency
class ShadcnButtonWidget extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ShadcnButtonVariant variant;
  final bool isLoading;
  final bool isExpanded;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool hapticFeedback;

  const ShadcnButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.variant = ShadcnButtonVariant.primary,
    this.isLoading = false,
    this.isExpanded = false,
    this.padding,
    this.width,
    this.height,
    this.hapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Convert our ButtonVariant to shadcn ButtonVariant
    final shadcnVariant = _convertToShadcnVariant(variant);
    
    Widget buttonWidget = Button(
      onPressed: isLoading ? null : _handlePress,
      variant: shadcnVariant,
      size: _getButtonSize(),
      child: _buildButtonContent(theme),
    );

    // Apply width constraints if specified
    if (width != null || isExpanded) {
      buttonWidget = SizedBox(
        width: isExpanded ? double.infinity : width,
        height: height,
        child: buttonWidget,
      );
    }

    // Apply padding if specified
    if (padding != null) {
      buttonWidget = Padding(
        padding: padding!,
        child: buttonWidget,
      );
    }

    return buttonWidget;
  }

  void _handlePress() {
    if (hapticFeedback) {
      HapticFeedback.selectionClick();
    }
    onPressed?.call();
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getLoadingColor(theme),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  ButtonVariant _convertToShadcnVariant(ShadcnButtonVariant variant) {
    switch (variant) {
      case ShadcnButtonVariant.primary:
        return ButtonVariant.primary;
      case ShadcnButtonVariant.secondary:
        return ButtonVariant.secondary;
      case ShadcnButtonVariant.outline:
        return ButtonVariant.outline;
      case ShadcnButtonVariant.ghost:
        return ButtonVariant.ghost;
      case ShadcnButtonVariant.destructive:
        return ButtonVariant.destructive;
    }
  }

  ButtonSize _getButtonSize() {
    if (height != null) {
      if (height! <= 32) return ButtonSize.sm;
      if (height! >= 48) return ButtonSize.lg;
    }
    return ButtonSize.md; // Default size
  }

  Color _getLoadingColor(ThemeData theme) {
    switch (variant) {
      case ShadcnButtonVariant.primary:
        return theme.colorScheme.onPrimary;
      case ShadcnButtonVariant.secondary:
        return theme.colorScheme.onSecondary;
      case ShadcnButtonVariant.outline:
      case ShadcnButtonVariant.ghost:
        return theme.colorScheme.primary;
      case ShadcnButtonVariant.destructive:
        return theme.colorScheme.onError;
    }
  }
}

/// Button variant enum that matches our existing design system
enum ShadcnButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  destructive,
}

/// Floating Action Button widget using shadcn design principles
class ShadcnFloatingActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final List<Color>? gradient;
  final bool mini;
  final String? tooltip;

  const ShadcnFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.gradient,
    this.mini = false,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget fab = Button(
      onPressed: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      variant: ButtonVariant.primary,
      size: mini ? ButtonSize.sm : ButtonSize.md,
      child: Icon(
        icon,
        size: mini ? 16 : 20,
        color: theme.colorScheme.onPrimary,
      ),
    );

    // Apply gradient decoration if specified
    if (gradient != null) {
      fab = Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient!,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(mini ? 20 : 28),
          boxShadow: [
            BoxShadow(
              color: gradient!.first.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              onPressed();
            },
            borderRadius: BorderRadius.circular(mini ? 20 : 28),
            child: Container(
              width: mini ? 40 : 56,
              height: mini ? 40 : 56,
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: Colors.white,
                size: mini ? 16 : 20,
              ),
            ),
          ),
        ),
      );
    }

    // Add tooltip if specified
    if (tooltip != null) {
      fab = Tooltip(
        message: tooltip!,
        child: fab,
      );
    }

    return fab;
  }
}
