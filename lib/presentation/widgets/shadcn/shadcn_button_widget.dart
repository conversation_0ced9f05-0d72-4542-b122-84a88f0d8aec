import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A shadcn-based button widget that replaces our custom AnimatedButton
/// while maintaining the same API and visual consistency
class ShadcnButtonWidget extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ShadcnButtonVariant variant;
  final bool isLoading;
  final bool isExpanded;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool hapticFeedback;

  const ShadcnButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.variant = ShadcnButtonVariant.primary,
    this.isLoading = false,
    this.isExpanded = false,
    this.padding,
    this.width,
    this.height,
    this.hapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget buttonWidget = ElevatedButton(
      onPressed: isLoading ? null : _handlePress,
      style: _getButtonStyle(theme),
      child: _buildButtonContent(theme),
    );

    // Apply width constraints if specified
    if (width != null || isExpanded) {
      buttonWidget = SizedBox(
        width: isExpanded ? double.infinity : width,
        height: height,
        child: buttonWidget,
      );
    }

    // Apply padding if specified
    if (padding != null) {
      buttonWidget = Padding(
        padding: padding!,
        child: buttonWidget,
      );
    }

    return buttonWidget;
  }

  void _handlePress() {
    if (hapticFeedback) {
      HapticFeedback.selectionClick();
    }
    onPressed?.call();
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.onPrimary,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  ButtonStyle _getButtonStyle(ThemeData theme) {
    Color backgroundColor;
    Color foregroundColor;

    switch (variant) {
      case ShadcnButtonVariant.primary:
        backgroundColor = theme.colorScheme.primary;
        foregroundColor = theme.colorScheme.onPrimary;
        break;
      case ShadcnButtonVariant.secondary:
        backgroundColor = theme.colorScheme.secondary;
        foregroundColor = theme.colorScheme.onSecondary;
        break;
      case ShadcnButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = theme.colorScheme.primary;
        break;
      case ShadcnButtonVariant.ghost:
        backgroundColor = Colors.transparent;
        foregroundColor = theme.colorScheme.onSurface;
        break;
      case ShadcnButtonVariant.destructive:
        backgroundColor = theme.colorScheme.error;
        foregroundColor = theme.colorScheme.onError;
        break;
    }

    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      minimumSize: Size(0, height ?? 40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: variant == ShadcnButtonVariant.outline
          ? BorderSide(color: theme.colorScheme.primary)
          : BorderSide.none,
      ),
      elevation: variant == ShadcnButtonVariant.ghost || variant == ShadcnButtonVariant.outline ? 0 : 2,
    );
  }
}

/// Button variant enum that matches our existing design system
enum ShadcnButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  destructive,
}

/// Floating Action Button widget using shadcn design principles
class ShadcnFloatingActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final List<Color>? gradient;
  final bool mini;
  final String? tooltip;

  const ShadcnFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.gradient,
    this.mini = false,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    Widget fab = FloatingActionButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      mini: mini,
      child: Icon(
        icon,
        size: mini ? 16 : 20,
      ),
    );

    // Apply gradient decoration if specified
    if (gradient != null) {
      fab = Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient!,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(mini ? 20 : 28),
          boxShadow: [
            BoxShadow(
              color: gradient!.first.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              onPressed();
            },
            borderRadius: BorderRadius.circular(mini ? 20 : 28),
            child: Container(
              width: mini ? 40 : 56,
              height: mini ? 40 : 56,
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: Colors.white,
                size: mini ? 16 : 20,
              ),
            ),
          ),
        ),
      );
    }

    // Add tooltip if specified
    if (tooltip != null) {
      fab = Tooltip(
        message: tooltip!,
        child: fab,
      );
    }

    return fab;
  }
}
