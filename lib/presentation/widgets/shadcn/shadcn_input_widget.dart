import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

/// A shadcn-based input widget that replaces our custom AuthTextFieldWidget
/// while maintaining the same API and visual consistency
class ShadcnInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final bool isPassword;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final bool enabled;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization textCapitalization;
  final FocusNode? focusNode;
  final bool required;
  final String? helperText;
  final String? errorText;

  const ShadcnInputWidget({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.isPassword = false,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.maxLength,
    this.inputFormatters,
    this.textCapitalization = TextCapitalization.none,
    this.focusNode,
    this.required = false,
    this.helperText,
    this.errorText,
  });

  @override
  State<ShadcnInputWidget> createState() => _ShadcnInputWidgetState();
}

class _ShadcnInputWidgetState extends State<ShadcnInputWidget> {
  bool _isPasswordVisible = false;
  bool _isFocused = false;
  String? _currentError;

  @override
  void initState() {
    super.initState();
    widget.focusNode?.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(_onFocusChange);
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = widget.focusNode?.hasFocus ?? false;
    });
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _onTextChanged(String value) {
    // Validate on change if validator is provided
    if (widget.validator != null) {
      setState(() {
        _currentError = widget.validator!(value);
      });
    }
    widget.onChanged?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.required ? '${widget.label} *' : widget.label,
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        
        // Input field
        Input(
          controller: widget.controller,
          focusNode: widget.focusNode,
          placeholder: widget.hint,
          keyboardType: widget.keyboardType,
          obscureText: widget.isPassword && !_isPasswordVisible,
          enabled: widget.enabled,
          maxLength: widget.maxLength,
          inputFormatters: widget.inputFormatters,
          textCapitalization: widget.textCapitalization,
          onChanged: _onTextChanged,
          prefix: widget.prefixIcon != null 
            ? Icon(
                widget.prefixIcon,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              )
            : null,
          suffix: _buildSuffix(theme),
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w400,
            height: 1.4,
          ),
        ),
        
        // Helper text or error
        if (widget.helperText != null || _currentError != null || widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Text(
              _currentError ?? widget.errorText ?? widget.helperText ?? '',
              style: theme.textTheme.bodySmall?.copyWith(
                color: (_currentError != null || widget.errorText != null)
                  ? theme.colorScheme.error
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget? _buildSuffix(ThemeData theme) {
    if (widget.isPassword) {
      return GestureDetector(
        onTap: _togglePasswordVisibility,
        child: Icon(
          _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          size: 16,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      );
    }
    
    if (widget.suffixIcon != null) {
      return Icon(
        widget.suffixIcon,
        size: 16,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
      );
    }
    
    return null;
  }
}

/// A shadcn-based textarea widget for multi-line input
class ShadcnTextAreaWidget extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final int maxLines;
  final int? minLines;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final bool enabled;
  final int? maxLength;
  final FocusNode? focusNode;
  final bool required;
  final String? helperText;
  final String? errorText;

  const ShadcnTextAreaWidget({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.maxLines = 4,
    this.minLines,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.maxLength,
    this.focusNode,
    this.required = false,
    this.helperText,
    this.errorText,
  });

  @override
  State<ShadcnTextAreaWidget> createState() => _ShadcnTextAreaWidgetState();
}

class _ShadcnTextAreaWidgetState extends State<ShadcnTextAreaWidget> {
  String? _currentError;

  void _onTextChanged(String value) {
    // Validate on change if validator is provided
    if (widget.validator != null) {
      setState(() {
        _currentError = widget.validator!(value);
      });
    }
    widget.onChanged?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.required ? '${widget.label} *' : widget.label,
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        
        // TextArea field
        TextArea(
          controller: widget.controller,
          focusNode: widget.focusNode,
          placeholder: widget.hint,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          enabled: widget.enabled,
          maxLength: widget.maxLength,
          onChanged: _onTextChanged,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w400,
            height: 1.4,
          ),
        ),
        
        // Helper text or error
        if (widget.helperText != null || _currentError != null || widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Text(
              _currentError ?? widget.errorText ?? widget.helperText ?? '',
              style: theme.textTheme.bodySmall?.copyWith(
                color: (_currentError != null || widget.errorText != null)
                  ? theme.colorScheme.error
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
