import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';

/// A shadcn-based progress widget that replaces LinearProgressIndicator
/// while maintaining the same API and visual consistency
class ShadcnProgressWidget extends StatelessWidget {
  final double? value;
  final Color? backgroundColor;
  final Color? color;
  final double height;
  final String? label;
  final bool showPercentage;
  final BorderRadius? borderRadius;

  const ShadcnProgressWidget({
    super.key,
    this.value,
    this.backgroundColor,
    this.color,
    this.height = 8,
    this.label,
    this.showPercentage = false,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label and percentage
        if (label != null || (showPercentage && value != null))
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (label != null)
                  Text(
                    label!,
                    style: theme.textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (showPercentage && value != null)
                  Text(
                    '${(value! * 100).round()}%',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
          ),
        
        // Progress bar
        Progress(
          value: value,
          backgroundColor: backgroundColor,
          foregroundColor: color,
        ),
      ],
    );
  }
}

/// A circular progress widget using shadcn design principles
class ShadcnCircularProgressWidget extends StatelessWidget {
  final double? value;
  final Color? backgroundColor;
  final Color? color;
  final double size;
  final double strokeWidth;
  final String? label;
  final bool showPercentage;
  final Widget? center;

  const ShadcnCircularProgressWidget({
    super.key,
    this.value,
    this.backgroundColor,
    this.color,
    this.size = 48,
    this.strokeWidth = 4,
    this.label,
    this.showPercentage = false,
    this.center,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Circular progress
        SizedBox(
          width: size,
          height: size,
          child: CircularProgress(
            value: value,
            backgroundColor: backgroundColor,
            foregroundColor: color,
            strokeWidth: strokeWidth,
            child: center ?? (showPercentage && value != null
              ? Text(
                  '${(value! * 100).round()}%',
                  style: theme.textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: size > 60 ? 12 : 10,
                  ),
                )
              : null),
          ),
        ),
        
        // Label
        if (label != null) ...[
          const SizedBox(height: 8),
          Text(
            label!,
            style: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// A step progress widget for multi-step processes
class ShadcnStepProgressWidget extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String>? stepLabels;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? completedColor;

  const ShadcnStepProgressWidget({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.stepLabels,
    this.activeColor,
    this.inactiveColor,
    this.completedColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Step indicators
        Row(
          children: List.generate(totalSteps, (index) {
            final isCompleted = index < currentStep;
            final isActive = index == currentStep;
            final isInactive = index > currentStep;
            
            return Expanded(
              child: Row(
                children: [
                  // Step circle
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isCompleted
                        ? (completedColor ?? theme.colorScheme.primary)
                        : isActive
                          ? (activeColor ?? theme.colorScheme.primary)
                          : (inactiveColor ?? theme.colorScheme.outline),
                    ),
                    child: Center(
                      child: isCompleted
                        ? Icon(
                            Icons.check,
                            size: 16,
                            color: theme.colorScheme.onPrimary,
                          )
                        : Text(
                            '${index + 1}',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: isActive
                                ? theme.colorScheme.onPrimary
                                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                    ),
                  ),
                  
                  // Connector line
                  if (index < totalSteps - 1)
                    Expanded(
                      child: Container(
                        height: 2,
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: isCompleted
                            ? (completedColor ?? theme.colorScheme.primary)
                            : (inactiveColor ?? theme.colorScheme.outline),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ),
        
        // Step labels
        if (stepLabels != null && stepLabels!.length == totalSteps) ...[
          const SizedBox(height: 12),
          Row(
            children: List.generate(totalSteps, (index) {
              final isCompleted = index < currentStep;
              final isActive = index == currentStep;
              
              return Expanded(
                child: Text(
                  stepLabels![index],
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: (isCompleted || isActive)
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }),
          ),
        ],
      ],
    );
  }
}

/// A loading skeleton widget using shadcn design principles
class ShadcnSkeletonWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Widget? child;

  const ShadcnSkeletonWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (child != null) {
      return Skeleton(child: child!);
    }
    
    return Skeleton(
      child: Container(
        width: width ?? double.infinity,
        height: height ?? 16,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }
}
