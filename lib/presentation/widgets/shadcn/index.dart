// Shadcn Flutter Widget Library
// This file exports all our custom shadcn-based widgets that replace
// the existing custom widgets while maintaining the same API and visual consistency

// Button widgets
export 'shadcn_button_widget.dart' show ShadcnButtonWidget, ShadcnFloatingActionButton, ShadcnButtonVariant;

// Input widgets
export 'shadcn_input_widget.dart' show ShadcnInputWidget, ShadcnTextAreaWidget;

// Card widgets
export 'shadcn_card_widget.dart' show ShadcnCardWidget, ShadcnDeckCardWidget, ShadcnStatsCardWidget;

// Progress widgets
export 'shadcn_progress_widget.dart' show ShadcnProgressWidget, ShadcnCircularProgressWidget, ShadcnSkeletonWidget;

// Navigation widgets
export 'shadcn_navigation_widget.dart' show ShadcnNavigationWidget, ShadcnNavigationItem;

// FlashCard widgets - only export the main widget to avoid conflicts
export 'shadcn_flashcard_widget.dart' show ShadcnFlashCardWidget;

// Re-export commonly used shadcn_flutter widgets for convenience
export 'package:shadcn_flutter/shadcn_flutter.dart' show
  // Core widgets
  Button,
  Input,
  TextArea,
  Card,
  Progress,
  CircularProgress,
  Skeleton,
  
  // Layout widgets
  Divider,
  Tabs,
  TabList,
  
  // Feedback widgets
  Alert,
  AlertDialog,
  Toast,
  
  // Navigation widgets
  NavigationMenu,
  Breadcrumb,
  
  // Form widgets
  Checkbox,
  Switch,
  RadioGroup,
  Select,
  Slider,
  
  // Data display widgets
  Avatar,
  AvatarGroup,
  Badge,
  
  // Utility widgets
  Dialog,
  Sheet,
  Popover,
  Tooltip,
  HoverCard,
  
  // Enums and types
  ButtonVariant,
  ButtonSize,
  ThemeDensity,
  ThemeScaling;
